Order creation API Flow

Buy Product 1 using user gift card balance:

Check balance

- if insuficient return error

- else, start db session
- with session deduct balance

Update other order related details (no db session used)

commit db session for balance update

---

This is my order creation flow, what if from another tab I purchase Product 2 (just after "- with session deduct balance") using the same gift card balance?
Can it happen that in the Product 1 "commit db session for balance update" step fails?

Refund calculate
individual link failure
