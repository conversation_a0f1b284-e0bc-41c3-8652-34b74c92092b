import {Application} from '../../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession} from '../../../dbTransactions'

declare const SiteUrl: string
declare const hashToUrl: (hash: string) => string

export class OrderRecovery {
  adminEmails: string[]
  constructor(private app: Application) {
    this.adminEmails = ['<EMAIL>']
  }

  // order completion with failure handling
  async processOrderCompletion(orderId: string, paymentDetails: any) {
    const startTime = Date.now()
    const order: any = await this.app.service('order').Model.findOne({_id: orderId})

    if (!order) {
      console.error(`Order not found: ${orderId}`)
      return
    }

    const maxRetryCount = 2
    const currentRetryCount = order.retryCount || 0

    console.log('COMP 1', order._id, order.retryCount, order.settled)
    if (currentRetryCount >= maxRetryCount) {
      await this.notifyFailedOrderAdmin(order)
      return
    }

    if (order.status !== 100) {
      console.warn(`Invalid order status. Status: ${order.status}`)
      return
    }
    // Retry count increment without transaction
    if (order.settled && currentRetryCount < maxRetryCount) {
      await this.app.service('order').Model.updateOne({_id: orderId}, {$inc: {retryCount: 1}})
    }

    // Update payment details without transaction
    if (order.status === 100 && !order.settled) {
      await this.app.service('order').patch(orderId, {...paymentDetails, $unset: {expiration: ''}})
    }

    let refundInfo: any = null
    const session = await startTransactionSession(this.app)
    try {
      // Complete order and get refund information
      const transactionParams = {mongoose: {session}}
      console.log('COMP 2', typeof transactionParams.mongoose.session)
      console.log('Time before complete order', Date.now() - startTime)
      refundInfo = await this.app.service('order').completeOrder(order, transactionParams)
      console.log('Time after complete order', Date.now() - startTime)

      // Update refundRequired field if refund is needed
      if (refundInfo.refundPrice && refundInfo.invalidLinks) {
        await this.app.service('order').Model.updateOne(
          {_id: orderId},
          {
            refundRequired: {
              refundPrice: refundInfo.refundPrice,
              invalidLinks: refundInfo.invalidLinks,
              createdAt: new Date(),
            },
          },
          {session}
        )
      }

      // Commit transaction
      console.log('COMP_AF 1')
      await commitTransactionSession(session)
      console.log('Time till commit', Date.now() - startTime)

      // Process refund outside transaction if needed
      if (refundInfo.refundPrice && refundInfo.invalidLinks) {
        await this.processRefund(orderId, refundInfo)
      }
      console.log('Time till end', Date.now() - startTime)
    } catch (error) {
      console.error('Order completion failed:', error)
      await rollbackTransactionSession(session)

      const updatedOrder = (await this.app.service('order').Model.findOne({_id: orderId})) as any
      const retryCount = updatedOrder?.retryCount || 0

      if (retryCount === 0) {
        await this.notifyFailedOrderCustomer(order)
      } else if (retryCount >= maxRetryCount) {
        await this.notifyFailedOrderAdmin(order)
        return // Don't throw error to stop Stripe/paypal from retrying
      }

      if (retryCount < maxRetryCount) {
        throw error // This will cause Stripe/paypal to retry
      }
    }
  }

  // send admin notification for failed order completion
  async notifyFailedOrderAdmin(order: any) {
    try {
      await Promise.all(
        this.adminEmails.map(async (email) => {
          await this.app.service('notice-tpl').send(
            'OrderCompletionFailed',
            {_id: '', email: email},
            {
              orderId: order._id.toString(),
              amount: order.price,
              url: `${SiteUrl}/v2/order/detail/${order._id}`,
              sys_url: `${SiteUrl}/v2/sys/order-failure-logs`,
            }
          )
        })
      )
    } catch (error) {
      console.error('Failed to send admin notification:', error)
    }
  }

  // send customer notification for failed order completion
  async notifyFailedOrderCustomer(order: any) {
    try {
      const user = await this.app.service('users').uidToInfo(order.buyer)
      const url = `${SiteUrl}/v2/order/detail/${order._id}`

      await this.app.service('notice-tpl').send(
        'OrderCompletionFailed',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          url: url,
        }
      )
    } catch (error) {
      console.error('Failed to send customer notification:', error)
    }
  }

  // Process refund for invalid links
  async processRefund(orderId: string, refundInfo: any, orderDoc?: any, isRetry?: boolean) {
    try {
      const order: any = orderDoc || (await this.app.service('order').Model.findOne({_id: orderId}))
      if (!order) {
        console.error(`Order not found for refund: ${orderId}`)
        return
      }

      const {refundPrice, refundLinkName, refundLinkCover} = refundInfo
      let refundSuccess = false

      // Process refund via payment provider
      if (order.payMethod.indexOf('paypal') > -1) {
        const refundResult: any = await this.app.service('paypal').get('refund', {
          query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)},
        })
        if (refundResult.success) {
          refundSuccess = true
        }
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundSuccess = true
      }

      if (refundSuccess) {
        await this.handleRefundSuccess(order, refundPrice, refundLinkName, refundLinkCover)
      } else {
        if (isRetry) throw new Error('Refund failed')
        await this.handleRefundFailure(orderId, refundPrice, refundLinkName)
      }
    } catch (error) {
      if (isRetry) throw new Error('Refund failed')
      console.error(`Error processing refund for order ${orderId}:`, error)
      await this.handleRefundFailure(orderId, refundInfo.refundPrice, refundInfo.refundLinkName)
    }
  }

  // Handle successful refund
  async handleRefundSuccess(order: any, refundPrice: number, refundLinkName: string[], refundLinkCover: string[]) {
    try {
      let refundList: any = []
      let notifyCustomer = false

      if (order.payMethod.indexOf('paypal') > -1) {
        refundList.push({
          method: 'paypal',
          amount: refundPrice,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: 503,
        })
        notifyCustomer = true
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundList.push({
          method: 'braintree',
          amount: refundPrice,
          createdAt: new Date(),
          executed: false,
          status: 503,
        })
      }
      await this.app.service('order').Model.updateOne(
        {_id: order._id},
        {
          $push: {refunds: {$each: refundList}},
          $unset: {refundRequired: ''},
        }
      )

      if (notifyCustomer) {
        // Send success notification to customer
        await this.sendRefundSuccessNotification(order, refundPrice, refundLinkName, refundLinkCover)
      }
    } catch (error) {
      console.error(`Failed to update order after successful refund ${order._id}:`, error)
    }
  }

  // Handle failed refund
  async handleRefundFailure(orderId: string, refundPrice: number, refundLinkName: string[]) {
    // Send admin notification for manual intervention
    await this.sendRefundFailureAdminNotification(orderId, refundPrice)

    // Send customer notification about delay
    await this.sendRefundDelayCustomerNotification(orderId, refundLinkName)
  }

  // Send refund success notification to customer
  async sendRefundSuccessNotification(order: any, refundPrice: number, refundLinkName: string[], refundLinkCover: string[]) {
    try {
      const user = await this.app.service('users').uidToInfo(order.buyer)
      const url = `${SiteUrl}/v2/order/payHistory/${order._id}`
      const url2 = `${SiteUrl}/v2/order/detail/${order._id}`

      await this.app.service('notice-tpl').send(
        'OrderRefundSuccess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          gift_card_amount: '0.00',
          cash_amount: (refundPrice / 100).toFixed(2),
          no: order.no,
          amount: (order.price / 100).toFixed(2),
          date: new Date(),
          url: url,
          link_name: refundLinkName.join(', '),
          url2: url2,
          image: hashToUrl(refundLinkCover[0] || ''),
          addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
        }
      )
    } catch (error) {
      console.error('Failed to send refund success notification:', error)
    }
  }

  // Send refund delay notification to customer
  async sendRefundDelayCustomerNotification(orderId: string, refundLinkName: string[]) {
    try {
      const order: any = await this.app.service('order').Model.findOne({_id: orderId})
      const user = await this.app.service('users').uidToInfo(order.buyer)
      const url = `${SiteUrl}/v2/order/detail/${orderId}`

      await this.app.service('notice-tpl').send(
        'OrderRefundInProcess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          link_name: refundLinkName.join('<br>'),
          url: url,
        }
      )
    } catch (error) {
      console.error('Failed to send refund delay notification:', error)
    }
  }

  // Send admin notification for refund failure
  async sendRefundFailureAdminNotification(orderId: string, refundPrice: number) {
    try {
      await Promise.all(
        this.adminEmails.map(async (email) => {
          await this.app.service('notice-tpl').send(
            'OrderRefundFailed',
            {_id: '', email: email},
            {
              orderId: orderId,
              amount: refundPrice,
              url: `${SiteUrl}/v2/order/detail/${orderId}`,
              sys_url: `${SiteUrl}/v2/sys/order-failure-logs`,
            }
          )
        })
      )
    } catch (error) {
      console.error('Failed to send admin notification:', error)
    }
  }

  // Retry failed refunds - cron job method
  async retryFailedRefunds() {
    try {
      // Find orders with pending refunds (older than 5 minutes to avoid immediate retries)
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      const ordersWithPendingRefunds = await this.app
        .service('order')
        .Model.find({
          'refundRequired.createdAt': {$lt: fiveMinutesAgo},
          'refundRequired.escalated': false,
        })
        .sort({'refundRequired.createdAt': 1})
        .limit(50)

      console.log(`Found ${ordersWithPendingRefunds.length} orders with pending refunds`)

      for (const orderDoc of ordersWithPendingRefunds) {
        try {
          const order: any = orderDoc
          const refundInfo = {
            refundPrice: order.refundRequired.refundPrice,
            invalidLinks: order.refundRequired.invalidLinks,
            refundLinkName: order.refundRequired.invalidLinks?.map((link: any) => link.name) || [],
            refundLinkCover: order.refundRequired.invalidLinks?.map((link: any) => link.cover) || [],
          }

          // For PayPal orders, check if refund already exists before retrying
          if (order.paypalId) {
            try {
              const refundCheck = await this.app.service('paypal').checkRefundExists(order.paypalId, refundInfo.refundPrice)
              if (refundCheck.exists) {
                console.log(`Refund already exists for PayPal order ${order._id}, marking as complete`)
                await this.handleRefundSuccess(order, refundInfo.refundPrice, refundInfo.refundLinkName, refundInfo.refundLinkCover)
                continue
              }
            } catch (error) {
              console.error(`Error checking PayPal refund status for order ${order._id}:`, error)
              // Continue with retry if we can't check status
            }
          }

          console.log(`Retrying refund for order ${order._id}, amount: ${refundInfo.refundPrice}`)
          await this.processRefund(order._id.toString(), refundInfo, orderDoc, true)
        } catch (error) {
          const order: any = orderDoc
          console.error(`Failed to retry refund for order ${order._id}:`, error)

          // If refund has been pending for more than 24 hours, escalate to admin
          const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          if (order.refundRequired.createdAt < twentyFourHoursAgo) {
            await this.sendRefundFailureAdminNotification(order._id.toString(), order.refundRequired.refundPrice)

            // Mark as escalated to prevent repeated admin notifications
            await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {'refundRequired.escalated': true}})
          }
        }
      }
    } catch (error) {
      console.error('Error in retryFailedRefunds cron job:', error)
    }
  }
}

/*
Admin dashboard APIs

Incomplete Orders (successful payment but failed to complete order)
App.service('order').find({
  status: 100,
  paidAt: {$exists: true}
})

Incomplete refunds (order completion successful, but failed to refund for removed products)
App.service('order').find({
  'refundRequired.createdAt': {$lt: fiveMinutesAgo}
})

order schema:
{
  retryCount: {type: Number, default: 0}, // 订单完成重试计数 | Order completion retry count
  // 待处理退款信息 | Pending refund information
  refundRequired: {
    refundPrice: {type: Number}, // 退款金额 | Refund amount in cents
    invalidLinks: {type: Array}, // 无效链接列表 | Invalid links array
    createdAt: {type: Date, default: Date.now}, // 创建时间 | Created timestamp
    escalated: {type: Boolean, default: false}, // 是否已 escalated | Escalated flag
  },
  status: 100,
  payMethod: {type: Array},
}


So using above apis we need to create a page for admin to see all orders that are in retry state and refund state.
In a page there should be two tabs:
1. Incomplete Orders
2. Incomplete Refunds

Each of them should have a table inside them:
1. Incomplete Orders:
  - OrderId
  - Status Code
  - Amount
  - Pay Method
  - Retry Count

2. Incomplete Refunds:
  - OrderId
  - Status Code
  - Amount
  - Pay Method
  - Refund Failed Time (refundRequired.createdAt)
  - Refund Amount (refundRequired.refundPrice)
  - Escalated (refundRequired.escalated)

We use vue3 + quasar. Also write code in latest vue format(do not use typescript or export syntax).
Call the api exactly as mentioned above, App is available globally. Create a beautiful admin dashboard style design.
Our primary color is teal.
No need to create vue app from scratch, simply create a component, which I will import and use in my project.
Also in Heading write "Order Disputes"
*/
