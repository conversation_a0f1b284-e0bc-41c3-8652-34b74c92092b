## Error handling

| Error Type                    | Example from Your Code                                    | Action          | Reason                                                                        |
| :---------------------------- | :-------------------------------------------------------- | :-------------- | :---------------------------------------------------------------------------- |
| **Write Conflict / Deadlock** | `updateOne` on `service-pack` income fails.               | **Retry**       | Classic timing issue. The conflict will likely be gone on the next attempt.   |
| **Network/DB Timeout**        | Any of the 100+ DB calls fails with a connection error.   | **Retry**       | Environmental issue. The connection will likely be restored.                  |
| **External API Failure**      | `unit CopyByDrive()` fails to connect to Google.          | **Retry**       | The external service is temporarily down.                                     |
| **Document Not Found**        | `service-pack findById` inside `buyByOrder` returns null. | **Full Refund** | The order is based on data that no longer exists. It's impossible to fulfill. |
| **Validation Error**          | A `pre` hook on a Mongoose schema throws an error.        | **Full Refund** | The data violates a permanent business rule.                                  |
| **Code Bug (`TypeError`)**    | `pack.statistic.find` fails because `statistic` is null.  | **Full Refund** | A bug in the code. Retrying is pointless and will fail again.                 |
