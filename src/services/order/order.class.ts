import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {OrderRecovery} from './lib/order-recovery'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class Order extends Service {
  app: Application
  private orderRecovery: OrderRecovery
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.orderRecovery = new OrderRecovery(app)
  }
  async buyCount({id, mode, type}: any, params: Params): Promise<any> {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    const con: any = {status: 200, 'links.id': id}
    if (mode) con['links.mode'] = mode
    if (type) con['links.type'] = type // old
    return await this.app.get('redisHCache')('OrderCount', id, async () => {
      return await this.Model.countDocuments(con, options)
    })
  }
  async buyInfo({id, mode, type}: any, params: Params): Promise<any> {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    const {_id} = params.user ?? {}
    const con: any = {status: 200, buyer: _id, 'links.id': id}
    if (mode) con['links.mode'] = mode
    if (type) con['links.type'] = type // old
    return await this.Model.findOne(con, null, options).select('links')
  }
  async extBuyer(one: any, params?: Params) {
    if (one.isSchool) {
      one.buyerInfo = await this.app.service('school-plan').get(one.buyer)
    } else {
      one.buyerInfo = await this.app.service('users').uidToInfo(one.buyer)
    }
    if (one.type == 'prompt') {
      for (let i = 0; i < one.links.length; i++) {
        if (one.links[i].style == 'prompt') {
          one.links[i].goods.userInfo = await this.app.service('users').uidToInfo(one.links[i].goods.uid)
        }
      }
    }
  }
  // 取消未支付订单
  async getCancelBeforePay({id, status = 400}: any, params: Params): Promise<any> {
    let order: any = await this.Model.findOne({_id: id})

    if (!order) {
      throw new NotFound('Order not found')
    }
    if (order.status !== 100) {
      throw new BadRequest('Order status error')
    }
    let links = order.links.map((e: any) => {
      e.removed = true
      return e
    })
    return await this.Model.updateOne({_id: id}, {status, links})
  }

  // 主题服务包 按代金券退款 无积分
  async getCancelTicket({tickets, status = 500}: any, params: Params): Promise<any> {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    let ticketData: any = await this.app.service('service-pack-ticket').Model.find({_id: {$in: tickets}}, null, options)
    // 验证tickets数据
    let orderId = ''
    for (let i = 0; i < ticketData.length; i++) {
      const e = ticketData[i]
      if (e.refund) {
        return Promise.reject(new GeneralError('Already refunded'))
      }
      if (!e.order) {
        return Promise.reject(new GeneralError('Order not found'))
      }
      if (!orderId) {
        orderId = e.order
      } else {
        if (orderId != e.order) {
          return Promise.reject(new GeneralError('Order not match'))
        }
      }
    }
    let order: any = await this.Model.findOne({_id: orderId}, null, options).lean()
    let linkDict: any = {}
    let refundPriceByService: any = {}
    order.links.forEach((e: any) => {
      linkDict[e.id] = e
    })

    // 计算退款金额
    let refundPrice = 0
    for (let i = 0; i < ticketData.length; i++) {
      // const ticket = ticketData[i]
      let {serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash} = serviceData[j]
        let itemRefundPrice = Number(((cash / linkDict[servicePack].count) * (linkDict[servicePack].price / linkDict[servicePack].persons)).toFixed(0))
        refundPrice += itemRefundPrice
        refundPriceByService[servicePack] = refundPriceByService[servicePack] ? refundPriceByService[servicePack] + itemRefundPrice : itemRefundPrice
      }
    }

    let refundList = []
    // braintree
    if (order.payMethod.indexOf('braintree') > -1 && refundPrice > 0) {
      if (order.settled) {
        let refundResult: any = await this.app.service('braintree').get('refund', {query: {id: order.braintreeId, amount: (refundPrice / 100).toFixed(2)}})
        if (refundResult.success) {
          refundList.push({
            method: 'braintree',
            amount: refundPrice,
            createdAt: new Date(),
            executedAt: new Date(),
            executed: true,
            status: status,
          })
        } else {
          return Promise.reject(new GeneralError(refundResult.message))
        }
      } else {
        refundList.push({
          method: 'braintree',
          amount: refundPrice,
          createdAt: new Date(),
          executed: false,
          status: status,
        })
      }
    }
    // paypal
    if (order.payMethod.indexOf('paypal') > -1 && refundPrice > 0) {
      let refundResult: any = await this.app.service('paypal').get('refund', {query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)}})
      if (refundResult.success) {
        refundList.push({
          method: 'paypal',
          amount: refundPrice,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      } else {
        return Promise.reject(new GeneralError(refundResult.message))
      }
    }
    // 已绑定用户ticket,user-data更新
    for (let i = 0; i < ticketData.length; i++) {
      let {uid, serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash, point, gift} = serviceData[j]
        if (uid) {
          let packUserData: any = await this.app.service('service-pack-user').Model.findOne({uid, 'snapshot._id': servicePack}, null, options)
          let unused = Number((cash + point + gift).toFixed(0))
          await this.app.service('service-pack-user-data').used(
            {
              packUser: packUserData._id,
              type: 'refund',
              times: unused,
              order: orderId,
            },
            params
          )
        }
        if (!uid) {
          await this.app.service('service-pack').Model.updateOne({_id: servicePack}, {$inc: {'count.ticket': -1}}, options)
        }
      }
    }

    // 更新link.refundPrice
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (refundPriceByService[link.id]) {
        link.refundPrice = (link.refundPrice + refundPriceByService[link.id]).toFixed(0)
      }
    }

    await this.app.service('order').patch(orderId, {$push: {refund: {$each: refundList}}, links: order.links}, {mongoose: {session: params.mongoose?.session}})
    await this.app.service('service-pack-ticket').Model.updateMany({_id: {$in: tickets}}, {refund: true, $unset: {uid: ''}}, options)
    await this.app.service('service-pack-apply').Model.updateMany({serviceTicket: {$in: tickets}}, {$pull: {serviceTicket: {$in: tickets}}}, options)

    // ticket全部退完以后 把订单剩余部分退款
    let notRefundTicket = await this.app.service('service-pack-ticket').Model.find({order: orderId, refund: false}, null, options)
    if (notRefundTicket.length == 0) {
      let links = order.links.map((e: any) => {
        if (e.style == 'service') {
          e.removed = true
        }
        return e
      })
      await this.app.service('order').patch(orderId, {links}, {mongoose: {session: params.mongoose?.session}})
      this.getCancel({id: orderId, status: status}, params)
    }
    return {ok: 1}
  }
  // 按link.id批量取消订单 用于公开课未成团/下架 取消订单
  async getCancelByLinkId({linkId, status}: any, params: Params): Promise<any> {
    let orders = await this.Model.find({status: 200, 'links.id': linkId})
    for (let i = 0; i < orders.length; i++) {
      let order: any = orders[i]
      this.app.service('order').getCancel({id: order._id, status: status}, params)
    }
    return {success: true}
  }

  /**
   * 订单取消并退款 整单处理
   * 学校购买的ticket,单独处理退款
   * linkId 不传或为空则全部退款
   * 除400外的4xx弃用
   * 401.未支付 公开课被讲师取消 canceled by the facilitator
   * 402.未支付 公开课因未成团被系统取消 Minimal registration number not met
   * 403.未支付 课件/自学习被下架 Product removed
   * 500.已支付 公开课/服务包被购买者取消 canceled by the purchaser
   * 501.已支付 公开课被讲师取消 canceled by the facilitator
   * 502.已支付 公开课因未成团被系统取消 Minimal registration number not met
   * 503.已支付 支付前被下架/删除,支付后立即退款
   */
  async getCancel({id, status, linkId}: any, params: Params): Promise<any> {
    let {refundAllowed, message, order, refundPrice, refundPoint, refundLinkName, refundLinkCover, refundLinkIds} = await this.getOrderRefundCheck({
      id,
      status,
      linkId,
    })
    if (!refundAllowed) {
      return Promise.reject(new GeneralError(message))
    }
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    if (order.type == 'service_premium' && order.isSchool && order.isTicket) {
      let tickets = await this.app.service('service-pack-ticket').Model.find({order: order._id, refund: false}, null, options).lean()
      if (tickets.length > 0) {
        return this.getCancelTicket({tickets: tickets.map((e) => e._id), status}, params)
      }
      // let serviceIds = order.links.filter((item: any) => !item.removed && item.style == 'service')
    }

    let user = await this.app.service('users').uidToInfo(order.buyer)
    if (order.isSchool) {
      user = await this.app.service('users').uidToInfo(order.schoolAdmin)
    }

    let refundList = []
    let isAllRefund = order.links.every((e: any) => e.removed)
    if (order.paid === 0) {
      let patchData: any = {links: order.links}
      if (isAllRefund) {
        patchData.status = status
      }
      this.app.service('order').patch(id, patchData)
      await this.handleLinkRefund(order, refundPrice, params)
      return {success: true, id, status}
    } else if (order.paid == 1) {
      // TODO gift card
      if (!order.isPoint) {
        // 现金退款 braintree
        if (order.payMethod.indexOf('braintree') > -1 && refundPrice > 0) {
          if (order.settled) {
            let refundResult: any = await this.app.service('braintree').get('refund', {query: {id: order.braintreeId, amount: (refundPrice / 100).toFixed(2)}})
            if (refundResult.success) {
              refundList.push({
                method: 'braintree',
                amount: refundPrice,
                createdAt: new Date(),
                executedAt: new Date(),
                executed: true,
                status: status,
              })
            } else {
              return Promise.reject(new GeneralError(refundResult.message))
            }
          } else {
            refundList.push({
              method: 'braintree',
              amount: refundPrice,
              createdAt: new Date(),
              executed: false,
              status: status,
            })
          }
        }
        // 现金退款 paypal
        if (order.payMethod.indexOf('paypal') > -1 && refundPrice > 0) {
          let refundResult: any = await this.app.service('paypal').get('refund', {query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)}})
          if (refundResult.success) {
            refundList.push({
              method: 'paypal',
              amount: refundPrice,
              createdAt: new Date(),
              executedAt: new Date(),
              executed: true,
              status: status,
            })
          } else {
            return Promise.reject(new GeneralError(refundResult.message))
          }
        }
      } else {
        // 积分退款
        await this.app.service('point-log').getAddLog({
          tab: 'earn',
          uid: order.buyer,
          source: 'refund',
          category: 'refund',
          change: refundPoint,
          businessId: order._id,
          snapshot: order,
        })
        refundList.push({
          method: 'point',
          amount: refundPoint,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      }
      let patchData: any = {links: order.links, $push: {refund: {$each: refundList}}}
      if (isAllRefund) {
        patchData.status = status
        patchData.paid = 2
      }
      await this.app.service('order').patch(id, patchData)
      await this.handleLinkRefund(order, refundPrice, params)

      // email refund success
      if (order.settled) {
        let url = `${SiteUrl}/v2/order/payHistory/${order._id}`
        let url2 = `${SiteUrl}/v2/order/detail/${order._id}`

        this.app.service('notice-tpl').send(
          'OrderRefundSuccess',
          {_id: user._id, email: user.email},
          {
            username: user.name.join(' '),
            gift_card_amount: '0.00',
            cash_amount: (refundPrice / 100).toFixed(2),
            no: order.no,
            amount: (order.price / 100).toFixed(2),
            date: new Date().toLocaleString(),
            url: url,
            link_name: refundLinkName.join(', '),
            url2: url2,
            image: hashToUrl(refundLinkCover[0] || ''),
            addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
          }
        )
      }
      return {success: true, id, status}
    }
  }
  // 退款后处理公开课和service相关业务解绑
  async handleLinkRefund(order: any, refundPrice: any, params: Params) {
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (link.pending) {
        if (link.style === 'unit') {
          if (!order.isPoint) {
            await this.app.service('unit').patch(link.id, {$inc: {income: link.price * -1}})
          }
        }
        if (link.style === 'session') {
          this.app.service('session').getUnReg({_id: link.id}, params)
          if (!order.isPoint) {
            this.app.service('session').patch(link.id, {$inc: {income: link.price * -1}})
          }
        }
        if (link.style === 'service' || link.style === 'service_substitute') {
          let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, 'snapshot._id': link.id, pid: {$exists: false}})
          if (link.isOnCampus) {
            packUser = await this.app.service('service-pack-user').Model.findOne({pid: packUser._id, country: link.country, city: link.city})
          }
          if (link.style === 'service') {
            if (order.isSchool) {
              continue
            }
            // 无主题
            let serviceCount = await this.calcServiceCount(packUser._id, order._id)
            // let times: any = (link.count + link.giftCount - packUser.used).toFixed(0)
            // this.app.service('service-pack-user').used({_id: packUser._id, times: times * 1, type: 'refund'})

            if (serviceCount.unused > 0) {
              await this.app.service('service-pack-user-data').used(
                {
                  packUser: packUser._id,
                  type: 'refund',
                  times: serviceCount.unused,
                  order: order._id,
                },
                params
              )
            }
          } else {
            // 代课
            let serviceCount = await this.calcServiceCountSubstitute(packUser, link, order.isPoint)
            await this.app.service('service-pack-user-data').usedSubstitute({packUser: packUser._id, times: serviceCount.unused, type: 'refund'}, params)
          }
          // if (link.promotion) {
          //   let userData: any = await this.app.service('users').Model.findOne({_id: order.buyer})
          //   let freeServiceType = userData.freeServiceType
          //   delete freeServiceType[link.id]
          //   await this.app.service('users').Model.updateOne({_id: order.buyer}, {freeServiceType})
          // }
          if (!order.isPoint) {
            let premiumType = 'all'
            await this.app
              .service('service-pack')
              .Model.updateOne(
                {_id: link.id, statistic: {$elemMatch: {count: link.count, isSchool: order.isSchool, type: premiumType, city: link.city}}},
                {$inc: {income: link.refundPrice * -1, 'statistic.$.income': link.refundPrice * -1}}
              )
          }
        }
        if (link.style === 'service_premium') {
          let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, order: order._id, premium: link.id})
          let serviceCount = await this.calcServiceCount(packUser._id, order._id)

          if (serviceCount.unused > 0) {
            await this.app.service('service-pack-user-data').used(
              {
                packUser: packUser._id,
                type: 'refund',
                times: serviceCount.unused,
                order: order._id,
              },
              params
            )
          }
        }
        delete link.pending
      }
    }

    // 解绑主题服务包面试数据
    if (order.servicePackApply) {
      this.app
        .service('service-pack-apply')
        .Model.updateOne({_id: order.servicePackApply}, {$unset: {interviewOrder: ''}})
        .exec()
    }
    if (!order.isPoint && order.servicePremium) {
      let premiumType = 'all'
      if (order.isSchool) {
        premiumType = this.getServicePremiumType(order.links)
      }
      await this.app
        .service('service-pack')
        .Model.updateOne(
          {_id: order.servicePremium, statistic: {$elemMatch: {count: 1, isSchool: order.isSchool, type: premiumType}}},
          {$inc: {income: refundPrice * -1, 'statistic.$.income': refundPrice * -1}}
        )
    }
  }
  // 检查订单能否退款 并生成退款信息
  // 500为手动取消,需判断退款条件,501-503为系统取消,无需判断退款条件
  async getOrderRefundCheck({id, status = 500, linkId}: any): Promise<any> {
    const order: any = await this.Model.findOne({_id: id})
    const {type} = order
    let refundPrice = 0
    let refundPoint = 0
    let refundLinkName = []
    let refundLinkCover = []
    let separateAllowed = true // 可结算 针对代课和task

    if (order.status !== 200) {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'Order status error',
      }
    }
    if (type == 'session_self_study') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for self-study contents enrolled from Classcipe Center.',
      }
    }
    if (type == 'unit') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for teaching resources purchased from Library.',
      }
    }
    if (type == 'premium_cloud') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for import cloud.',
      }
    }
    if (type == 'prompt') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for prompt.',
      }
    }

    // 需退款的link
    let refundLinkIds = []
    if (!linkId || linkId.length == 0) {
      refundLinkIds = order.links.map((item: any) => item.id)
    } else {
      refundLinkIds = linkId
    }
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (link.removed) {
        continue
      }
      if (!refundLinkIds.includes(link.id)) {
        continue
      }
      // 可退款判定 并同意退款
      if (link.style === 'session') {
        if (status == 500) {
          let session = await this.app.service('session').get(link.id)
          if (session.start && new Date(session.start).getTime() - 12 * 60 * 60 * 1000 < Date.now()) {
            return {
              refundAllowed: false,
              separateAllowed: true,
              message: 'Free cancellation within 12 hours before the start of the workshop.',
            }
          }
        }
        if (!order.isPoint) {
          refundPrice += link.price
          link.refundPrice = link.price
        } else {
          refundPoint += link.point
          link.refundPoint = link.point
        }
      } else if (link.style === 'service') {
        // 无主题
        if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
          return {
            refundAllowed: false,
            separateAllowed: true,
            message: 'Free cancellation of the remaining within 14 days.',
          }
        }
        if (!order.isSchool) {
          let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, 'snapshot._id': link.id})
          let serviceCount = await this.calcServiceCount(packUser._id, order._id)
          let remainingCount = order.settled ? serviceCount.refundCount : 0

          if (!order.isPoint) {
            // refundPrice += link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
            // link.refundPrice = link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
            let calcRefundPrice = this.calcPriceServiceRefund({link, remainingCount, order})
            refundPrice += calcRefundPrice
            link.refundPrice = calcRefundPrice
          } else {
            refundPoint += link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
            link.refundPoint = link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
          }
        }
      } else if (link.style === 'service_substitute') {
        // 代课 永久可退 30天后扣除总金额30%
        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, 'snapshot._id': link.id, pid: {$exists: false}})
        if (link.isOnCampus) {
          packUser = await this.app.service('service-pack-user').Model.findOne({pid: packUser._id, country: link.country, city: link.city})
        }
        // else {
        //   if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
        //     return {
        //       refundAllowed: false,
        //       message: 'Free cancellation of the remaining within 14 days.',
        //     }
        //   }
        // }
        let isOver30Days = false
        if (new Date(order.createdAt).getTime() + 30 * 24 * 60 * 60 * 1000 < Date.now()) {
          isOver30Days = true
        } else {
          separateAllowed = false
        }
        if (!packUser) {
          continue
        }
        let serviceCount = await this.calcServiceCountSubstitute(packUser, link, order.isPoint)
        let remainingCount = order.settled ? serviceCount.refundCount : 0
        if (!order.isPoint) {
          let itemRefundPrice = link.count ? Number(((remainingCount / (link.count * 60)) * link.price * (isOver30Days ? 0.7 : 1)).toFixed(0)) : 0
          refundPrice += itemRefundPrice
          link.refundPrice = itemRefundPrice
        } else {
          let itemRefundPoint = link.count ? Number(((remainingCount / (link.count * 60)) * link.point * (isOver30Days ? 0.7 : 1)).toFixed(0)) : 0
          refundPoint += itemRefundPoint
          link.refundPoint = itemRefundPoint
        }
      } else if (link.style === 'service_premium') {
        if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
          return {
            refundAllowed: false,
            separateAllowed: true,
            message: 'Free cancellation of the remaining within 14 days.',
          }
        }

        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, order: order._id, premium: link.id})
        if (!packUser) {
          continue
        }
        let serviceCount = await this.calcServiceCount(packUser._id, order._id)
        let remainingCount = order.settled ? serviceCount.refundCount : 0
        if (!order.isPoint) {
          refundPrice += link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
          link.refundPrice = link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
        } else {
          refundPoint += link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
          link.refundPoint = link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
        }
      } else {
        if (!order.isPoint) {
          refundPrice += link.price
          link.refundPrice = link.price
        } else {
          refundPoint += link.point
          link.refundPoint = link.point
        }
      }
      refundLinkName.push(link.name)
      refundLinkCover.push(link.cover)
      link.removed = true
      link.pending = true
    }
    return {
      refundAllowed: true,
      separateAllowed,
      message: '',
      order,
      refundPrice,
      refundPoint,
      refundLinkName,
      refundLinkCover,
      refundLinkIds,
    }
  }

  // 计算服务包退款价格 已使用按原始价格扣除(不计算批发价,只计算折扣) #5814
  calcPriceServiceRefund({link, remainingCount, order}: any) {
    if (remainingCount <= 0) {
      return 0
    }
    let {price, goods, count} = link
    let {price: unitPrice, discountConfig} = goods
    let isDiscountConfig =
      discountConfig && discountConfig.enable && (!discountConfig.end || new Date(order.createdAt).getTime() < new Date(discountConfig.end).getTime())

    let refundPrice = 0
    refundPrice = count ? Number((price - unitPrice * (count - remainingCount) * (isDiscountConfig ? (100 - discountConfig.discount) / 100 : 1)).toFixed(0)) : 0

    return refundPrice > 0 ? refundPrice : 0
  }
  // 检查商品列表是否可购买
  async getCheckLinks({links = [], servicePremium, sharedSchool}: any, params: Params): Promise<any> {
    const {_id} = params.user ?? {}
    let ordered: String[] = []

    let unpaidOrder = []
    let order: any = []
    if (servicePremium) {
      unpaidOrder = await this.Model.find({
        buyer: _id,
        status: 100,
        servicePremium,
        sharedSchool,
      })
      order = await this.Model.find({
        buyer: _id,
        status: {$in: [200]},
        servicePremium,
        sharedSchool,
      })
    } else {
      // check paid order
      let existLinkIds = links.filter((item: any) => item.style === 'unit' || item.style === 'session').map((item: any) => item.id)
      order = await this.Model.find({
        buyer: _id,
        status: {$in: [200]},
        links: {$elemMatch: {id: {$in: existLinkIds}, removed: {$exists: false}}},
        type: {$ne: 'service_premium'},
      })
      order.forEach((item: any) => {
        item.links.forEach((link: any) => {
          if (existLinkIds.indexOf(link.id) > -1 && !link.removed) {
            ordered.push(link.id)
          }
        })
      })
      // check unpaid order
      let unpaidLinkIds = links.map((item: any) => item.id)
      unpaidOrder = await this.Model.find({
        buyer: _id,
        status: 100,
        links: {$elemMatch: {id: {$in: unpaidLinkIds}, removed: {$exists: false}}},
        type: {$ne: 'service_premium'},
      })
      unpaidOrder.forEach((item: any) => {
        item.links.forEach((link: any) => {
          if (existLinkIds.indexOf(link.id) > -1 && !link.removed) {
            ordered.push(link.id)
          }
        })
      })
    }

    for (let i = 0; i < links.length; i++) {
      const item = links[i]
      if (ordered.indexOf(item.id) > -1) {
        item.ordered = true
      }
      if (item.style === 'unit') {
        let unit
        try {
          unit = await this.app.service('unit').get(item.id)
        } catch (error) {
          unit = false
        }
        if (!unit || !unit.publish.lib) {
          item.removed = true
        }
      } else if (item.style === 'session') {
        let session
        try {
          session = await this.app.service('session').get(item.id)
        } catch (error) {
          session = false
        }
        if (!session) {
          item.removed = true
        }
        if (session.type !== 'selfStudy' && Date.now() > new Date(session.regDate).getTime()) {
          item.removed = true
        }
      } else if (item.style === 'service' || item.style === 'service_substitute') {
        let service
        try {
          service = await this.app.service('service-pack').get(item.id)
        } catch (error) {
          service = false
        }
        if (!service || !service.status) {
          item.removed = true
        }
      } else if (item.style === 'service_premium') {
        let auth
        try {
          auth = await this.app.service('service-auth').Model.findOne({_id: item.id})
        } catch (error) {
          auth = false
        }

        if (!auth) {
          item.removed = true
        }
      }
    }
    let available = links.filter((item: any) => !item.removed && ordered.indexOf(item.id) == -1).map((item: any) => item.id)
    let notExist = links.filter((item: any) => item.removed).map((item: any) => item.id)
    let paidOrderId = order.map((item: any) => item._id)
    let unpaidOrderId = unpaidOrder.map((item: any) => item._id)
    let orderId = [...paidOrderId, ...unpaidOrderId]
    let servicePremiumAvailable = servicePremium && !notExist.length && !unpaidOrder.length && !order.length
    return {links, available, notExist, ordered, orderId, paidOrderId, unpaidOrderId, servicePremiumAvailable}
  }
  // paypal支付完成后 前端主动回调
  async getCheckPaypalPayment({id, paypalOrderId}: any, params: Params): Promise<any> {
    let paypalDetail: any
    try {
      paypalDetail = await this.app.service('paypal').paypalOrderRequest(paypalOrderId)
    } catch (error) {
      return Promise.reject(new GeneralError(error))
    }
    if (paypalDetail.status === 'COMPLETED') {
      console.log('COMP 0', paypalDetail.purchase_units[0].payments.captures[0].id)
      await this.processOrderCompletion(id, {
        paypalId: paypalDetail.purchase_units[0].payments.captures[0].id,
        settled: true,
        $push: {payMethod: 'paypal'},
        paymentInfo: {
          paymentInstrumentType: 'paypal_account',
          cardType: '',
          last4: '',
        },
        paidAt: new Date(),
      })
      return true
    } else {
      return false
    }
  }

  // async excGoods(order: any, params?: Params) {
  //   if (order.status !== 400) {
  //     return order
  //   }
  //   for (let i = 0; i < order.links.length; i++) {
  //     let link = order.links[i]
  //     let goods
  //     try {
  //       if (link.style === 'session') {
  //         goods = await this.app.service('session').get(link.id)
  //       } else if (link.style === 'unit') {
  //         goods = await this.app.service('unit').get(link.id)
  //       }
  //     } catch (e) {
  //       goods = {}
  //     }
  //     link.goods = goods
  //   }
  //   return order
  // }
  async expirationDate(order: any, params?: Params) {
    if (order.status !== 100) {
      return order
    }
    let expirationArr = []
    for (let i = 0; i < order.links.length; i++) {
      const link = order.links[i]
      if (order.type === 'session_public' && link.style === 'session') {
        let session = await this.app.service('session').get(link.id)
        if (session.regDate) {
          expirationArr.push(new Date(session.regDate).getTime())
        }
      }
    }
    expirationArr.push(new Date(order.createdAt).getTime() + 12 * 60 * 60 * 1000)
    return expirationArr.sort().shift()
  }
  // 支付后检查商品,计算退款信息但不执行退款 积分购买无需检查(下单扣费一步完成)
  async getRefundInvalidLinks({orderId}: any, params: any) {
    let order: any = await this.app.service('order').Model.findOne({_id: orderId}).lean()
    let myLinks = Acan.clone(order.links)
    let sessionRemoved = false
    let refundLinkName: any = []
    let refundLinkCover: any = []
    let servicePremiumData: any
    if (order.servicePremium) {
      servicePremiumData = await this.app.service('service-pack').Model.findOne({_id: order.servicePremium}).lean()
    }
    for (let i = 0; i < myLinks.length; i++) {
      const item = myLinks[i]
      if (Acan.isObjectId(item.id)) {
        if (item.style === 'unit') {
          let unit: any = await this.app.service('unit').Model.findOne({_id: item.id}).lean()
          if (!unit || !unit.publish.lib) {
            item.removed = true
            item.refundPrice = item.price
            refundLinkName.push(item.name)
            refundLinkCover.push(item.cover)
          }
        } else if (item.style === 'session') {
          let session: any = await this.app.service('session').Model.findOne({_id: item.id}).lean()
          if (!session) {
            item.removed = true
            item.refundPrice = item.price
            sessionRemoved = true
            refundLinkName.push(item.name)
            refundLinkCover.push(item.cover)
          }
        } else if (item.style === 'service' || item.style === 'service_substitute') {
          let service: any = await this.app.service('service-pack').Model.findOne({_id: item.id}).lean()
          if (!service || !service.status || (order.servicePremium && !servicePremiumData?.status)) {
            item.removed = true
            item.refundPrice = item.price
            refundLinkName.push(item.name)
            refundLinkCover.push(item.cover)
          }
        } else if (item.style === 'service_premium') {
          let lecture: any = await this.app.service('service-auth').Model.findOne({_id: item.id}).lean()
          if (!lecture || lecture.status != 2 || (order.servicePremium && !servicePremiumData?.status)) {
            item.removed = true
            item.refundPrice = item.price
            refundLinkName.push(item.name)
            refundLinkCover.push(item.cover)
          }
        }
      }
    }
    // TODO gift card
    if (order.type == 'session_service_pack' && sessionRemoved) {
      myLinks = myLinks.map((item: any) => {
        if (item.style === 'service') {
          item.removed = true
          item.refundPrice = item.price
          refundLinkName.push(item.name)
        }
        return item
      })
    }
    let invalidLinks = myLinks.filter((item: any) => item.removed)
    let refundPrice = invalidLinks.reduce((prev: any, cur: any) => {
      return prev + cur.price
    }, 0)

    // Return refund calculation results without performing actual refund
    return {
      refundPrice,
      invalidLinks,
      refundLinkName,
      refundLinkCover,
    }
  }
  // 订单数量统计
  async getCount({}: any, params: Params): Promise<any> {
    let unpaid = await this.Model.count({buyer: params.user?._id, status: 100})
    let paid = await this.Model.count({buyer: params.user?._id, status: 200})
    return {
      unpaid,
      paid,
    }
  }

  // 价格计算 unit/session
  async calcPrice(discount: any, isPoint = false, style: string, goods: any, params?: Params): Promise<any> {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    if (!discount) {
      return 0
    }
    let price = 0
    if (style == 'self_study') {
      let incomeSetting: any = await this.app.service('income-setting').Model.findOne({tab: 'claim', category: 'self_study'}, null, options)
      if (incomeSetting) {
        price = Number((incomeSetting.value * goods.questions.length).toFixed(0))
      } else {
        price = 0
      }
    } else {
      if (discount.val) {
        if (discount.end && new Date(discount.end).getTime() < Date.now()) {
          price += Number(discount.price || 0)
        } else {
          price += ((discount.price || 0) * (100 - discount.val)) / 100
        }
      } else {
        price += Number(discount.price || 0)
      }
    }
    if (!isPoint) {
      return {
        success: true,
        price: Number(price.toFixed(0)),
        point: 0,
      }
    } else {
      return await this.app.service('point-setting').calcPoint({type: {category: style}, amount: price, isPoint: false}, params)
    }
  }
  // 服务包价格计算
  async calcPriceService({discount, unitPrice, count, discountConfig, isPoint, persons = 1, noDiscount, style}: any, params?: Params) {
    let price = 0
    let current: any = false
    let giftCount = 0
    let discountSort = discount.sort((a: any, b: any) => a.count - b.count)
    let isDiscountConfig =
      discountConfig && discountConfig.enable && discountConfig.discount && (!discountConfig.end || Date.now() < new Date(discountConfig.end).getTime())
    for (let i = 0; i < discountSort.length; i++) {
      const item = discountSort[i]
      if (count >= item.count) {
        current = item
      } else if (count < item.count) {
        break
      }
    }

    // 批发
    if (current) {
      giftCount = current.gifts
    }
    if (current && !noDiscount) {
      price += (unitPrice * count * (100 - current.discount)) / 100
    } else {
      price += Number(unitPrice * count)
    }
    // 折扣
    if (isDiscountConfig) {
      price = (price * (100 - discountConfig.discount)) / 100
    }
    price = price * persons

    if (!isPoint) {
      return {
        success: true,
        price: Number(price.toFixed(0)),
        point: 0,
        giftCount,
      }
    } else {
      let res = await this.app.service('point-setting').calcPoint({type: {category: style}, amount: price, isPoint: false}, params)
      return {
        ...res,
        giftCount,
      }
    }
  }
  // 主题服务包价格计算
  async calcPriceServicePremium(
    {
      premium,
      goods,
      sharedSchool,
      isPoint,
      isSchool,
      count,
      noDiscount,
      buyer,
    }: {
      premium: string
      goods: any
      sharedSchool: any
      isPoint: boolean
      isSchool: boolean
      count: any
      noDiscount: boolean
      buyer: string
    },
    params?: Params
  ): Promise<any> {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    let contentOrientated
    let current: any
    let price = 0
    let discountConfig = goods.discountConfig
    if (sharedSchool) {
      let schoolPrice: any = await this.app.service('service-pack-apply').Model.findOne({uid: buyer, sharedSchool, servicePack: goods._id}, null, options)
      contentOrientated = schoolPrice.contentOrientated
    } else {
      contentOrientated = goods.contentOrientated
    }
    contentOrientated.forEach((e: any) => {
      if (e.premium == premium) {
        current = e
      }
    })
    price = Number(((isSchool ? current.schoolPrice : current.price) * (count || current.times)).toFixed(0))
    if (!sharedSchool && discountConfig.enable && (!discountConfig.end || Date.now() < new Date(discountConfig.end).getTime()) && !noDiscount) {
      price = Number(((price * (100 - discountConfig.discount)) / 100).toFixed(0))
    }
    if (!isPoint) {
      return {
        success: true,
        price,
        point: 0,
        count: count || current.times,
      }
    } else {
      let res = await this.app.service('point-setting').calcPoint({type: {category: 'service_premium'}, amount: price, isPoint: false}, params)
      return {
        ...res,
        count: count || current.times,
      }
    }
  }
  // 订单settled后 待处理link执行退款
  async dispatchRefund(order: any, params: any) {
    let refundList = order.refund
    let refundLinkName = order.links.filter((e: any) => e.removed).map((e: any) => e.name)
    let refundLinkCover = order.links.filter((e: any) => e.removed).map((e: any) => e.cover)
    const user = await this.app.service('users').uidToInfo(order.buyer)
    for (let i = 0; i < refundList.length; i++) {
      const item = refundList[i]
      if (item.method === 'braintree') {
        let refundResult: any = await this.app
          .service('braintree')
          .get('refund', {query: {id: order.braintreeId, amount: (parseFloat(item.amount) / 100).toFixed(2)}})
        if (refundResult.success) {
          refundList[i].executed = true
          refundList[i].executedAt = new Date()
          // email refund success
          let url = `${SiteUrl}/v2/order/payHistory/${order._id}`
          let url2 = `${SiteUrl}/v2/order/detail/${order._id}`
          this.app.service('notice-tpl').send(
            'OrderRefundInProcess',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              url: url,
              link_name: refundLinkName.join('<br>'),
            }
          )
          this.app.service('notice-tpl').send(
            'OrderRefundSuccess',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              gift_card_amount: '0.00',
              cash_amount: (parseFloat(item.amount) / 100).toFixed(2),
              no: order.no,
              amount: (parseFloat(order.price) / 100).toFixed(2),
              date: new Date(),
              url: url,
              link_name: refundLinkName.join(', '),
              url2: url2,
              image: hashToUrl(refundLinkCover[0] || ''),
              addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
            }
          )
        }
      }
    }
    this.app.service('order').patch(order._id, {$set: {refund: refundList}})
  }

  // 判断主题服务包的类型 购买了哪种服务包 'lecture', 'mentor', 'all'
  getServicePremiumType(links: string): string {
    let hasService = false
    let hasPremium = false
    for (let i = 0; i < links.length; i++) {
      const link: any = links[i]
      if (link.style === 'service' || link.style === 'service_substitute') {
        hasService = true
      }
      if (link.style === 'service_premium') {
        hasPremium = true
      }
    }
    if (hasService && hasPremium) {
      return 'all'
    } else if (hasService) {
      return 'mentor'
    } else if (hasPremium) {
      return 'lecture'
    } else {
      return 'no type'
    }
  }

  // Expose as public method
  async processOrderCompletion(orderId: string, paymentDetails: any) {
    return this.orderRecovery.processOrderCompletion(orderId, paymentDetails)
  }

  // 支付后更新订单status,paid
  async completeOrder(order: any, params?: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    const transactionParams = params?.mongoose?.session ? {mongoose: {session: params?.mongoose?.session}} : {}
    let {_id, links, type, isPoint, isSchool, isTicket, persons, buyer, servicePremium, sharedSchool, servicePackApply} = order
    let user
    let noticeUser
    if (!isSchool) {
      user = Acan.clone(await this.app.service('users').uidToInfo(order.buyer))
      user._id = user._id.toString()
      noticeUser = user
    } else {
      noticeUser = Acan.clone(await this.app.service('users').uidToInfo(order.schoolAdmin))
      noticeUser._id = noticeUser._id.toString()
      user = {_id: buyer}
    }

    let refundInfo = await this.app.service('order').getRefundInvalidLinks({orderId: order._id}, {})
    let {refundPrice, invalidLinks, refundLinkName, refundLinkCover} = refundInfo

    let invalidLinksId = invalidLinks.map((item: any) => item.id)
    for (let i = 0; i < links.length; i++) {
      const item = links[i]
      const invalidIndex = invalidLinksId.indexOf(item.id)
      if (invalidIndex === -1) {
        if (item.style === 'unit') {
          if (!isPoint) {
            await this.app.service('unit').patch(item.id, {$inc: {income: item.price}}, params)
          }
          try {
            let post = await this.app.service('unit').copy({_id: item.id, orderId: order._id}, {user: {_id: order.buyer}, ...transactionParams})
            item.newId = post._id
            item.hash = post.sid
          } catch (e) {}
        }
        if (item.style === 'session') {
          const reg = {avatar: user.avatar, nickname: await this.app.service('users').nameFormatter({user}), _id: user._id, order: order._id}
          if (!isPoint) {
            await this.app.service('session').patch(item.id, {$inc: {income: item.price}}, {user, ...transactionParams})
          }
          await this.app.service('session').patch(item.id, {_date: new Date(item.goods.start).toString(), $addToSet: {reg}}, {user, ...transactionParams})
        }
        if (item.style === 'service' || item.style === 'service_substitute') {
          let insertData: any = {
            packId: item.id,
            order: order._id.toString(),
            total: item.count,
            price: item.price,
            // gift: item.gift ? true : false,
            giftCount: item.giftCount,
            point: item.point,
            isPoint: isPoint,
            isPromotion: item.promotion,
          }
          if (item.sessionId) {
            insertData.session = item.sessionId
          }
          // let packRes = await this.app.service('service-pack-user').buyByOrder(insertData, {user})
          // if (item.giftCount) {
          //   this.app.service('service-pack-user').used({_id: packRes._id, times: item.giftCount * -1, type: 'gift'})
          // }

          // session捆包服务包类型订单 需判定session是否promotion 是:服务包次数都为gift,否:正常加次数(cash/point)
          if (type === 'session_service_pack') {
            let sessionDoc: any = await this.app.service('session').Model.findOne({_id: item.sessionId}, null, options)
            if (sessionDoc.promotion) {
              insertData.giftCount = item.count + item.giftCount
              insertData.total = 0
            }
          }

          if (item.style === 'service') {
            if (!isSchool) {
              await this.completeService(insertData, user, transactionParams)
            }
          } else {
            if (item.isOnCampus) {
              insertData.country = item.country
              insertData.city = item.city
            }
            insertData.isOnCampus = item.isOnCampus
            await this.completeServiceSubstitute(insertData, user, transactionParams)
          }
          // 更新income 各数量订单数统计
          if (!isPoint) {
            let pack: any = await this.app.service('service-pack').Model.findOne({_id: item.id}).lean()
            let statistic = pack.statistic || []
            let premiumType = 'all'
            let isFind = statistic.find((e: any) => e.count == item.count && e.isSchool == isSchool && e.type == premiumType && e.city == item.city)
            if (isFind) {
              await this.app.service('service-pack').Model.updateOne(
                {
                  _id: item.id,
                  statistic: {
                    $elemMatch: {count: item.count, isSchool: isSchool, type: premiumType, city: item.city},
                  },
                },
                {$inc: {'statistic.$.orderCount': 1, 'statistic.$.income': item.price}},
                options
              )
            } else {
              await this.app
                .service('service-pack')
                .Model.updateOne(
                  {_id: item.id},
                  {$push: {statistic: {count: item.count, orderCount: 1, income: item.price, isSchool, type: premiumType, city: item.city}}},
                  options
                )
            }
            await this.app.service('service-pack').Model.updateOne({_id: item.id}, {$inc: {income: item.price}}, options)
          }
        }
        if (item.style === 'service_premium') {
          let insertData: any = {
            premium: item.id,
            packId: servicePremium,
            order: order._id.toString(),
            total: item.count,
            price: item.price,
            giftCount: item.giftCount,
            point: item.point,
            isPoint: isPoint,
            isPromotion: item.promotion,
            packUserTasks: item.packUserTasks,
          }
          if (item.oldPackUser) {
            insertData.oldPackUser = item.oldPackUser
          }
          if (!isSchool) {
            if (!sharedSchool) {
              await this.completeServicePremium(insertData, user, transactionParams)
            }
          } else {
            await this.completeServicePremium(insertData, {_id: buyer}, transactionParams)
          }
        }
        if (item.style === 'premium_cloud') {
          let sessionDoc = await this.app
            .service('service-booking')
            .importByBooking({serviceAuthId: item.id, bookingId: item.bookingId, order: _id.toString()}, {user, ...transactionParams})
          item.session = sessionDoc._id
          item.used = true
        }
      } else {
        // Use updated links from refund calculation
        links[i] = invalidLinks[invalidIndex]
      }
    }

    let status = 200
    let paid = 1

    if (invalidLinks.length == links.length) {
      status = 503
      paid = 2
    } else {
      // email pay success
      let url = `${SiteUrl}/v2/order/receipt/${order._id}`
      let url2 = `${SiteUrl}/v2/order/detail/${order._id}`
      this.app.service('notice-tpl').send(
        'OrderPaySuccess',
        {_id: noticeUser._id, email: noticeUser.email},
        {
          name: `${links[0].name}${links.length > 1 ? ' +' + (links.length - 1) + ' products' : ''}`,
          url: url,
          url2: url2,
          image: hashToUrl(links[0].cover || ''),
        }
      )

      // 生成预期积分佣金log
      this.settleByOrder({oid: order._id, status: 0})
    }
    if (servicePackApply) {
      this.app.service('service-pack-apply').Model.updateOne({_id: servicePackApply}, {interviewOrder: order._id}, options).exec()
    }
    if (servicePremium) {
      this.app.service('service-pack-apply').updateOrderInfo({uid: buyer, servicePack: servicePremium, sharedSchool, order: order._id}, transactionParams)

      // 更新income 各数量订单数统计
      if (!isPoint) {
        let pack: any = await this.app.service('service-pack').Model.findOne({_id: servicePremium}, null, options).lean()
        let statistic = pack.statistic || []
        let premiumType = 'all'
        if (isSchool) {
          premiumType = this.getServicePremiumType(links)
        }
        let isFind = statistic.find((e: any) => e.count == 1 && e.isSchool == isSchool && e.type == premiumType)
        if (isFind) {
          await this.app
            .service('service-pack')
            .Model.updateOne(
              {_id: servicePremium, statistic: {$elemMatch: {count: 1, isSchool: isSchool, type: premiumType}}},
              {$inc: {'statistic.$.orderCount': 1, 'statistic.$.income': order.price}},
              options
            )
        } else {
          await this.app
            .service('service-pack')
            .Model.updateOne({_id: servicePremium}, {$push: {statistic: {count: 1, orderCount: 1, income: order.price, isSchool, type: premiumType}}}, options)
        }
        await this.app.service('service-pack').Model.updateOne({_id: servicePremium}, {$inc: {income: order.price}}, options)
      }
    }
    if (isTicket) {
      this.app.service('service-pack-ticket').generate({persons, school: buyer, servicePremium, order: order._id, links}, transactionParams)
    }
    this.app.service('order').patch(order._id, {status, paid, links}, transactionParams)

    // Return refund information if refund is required
    return {
      refundPrice: refundPrice > 0 ? refundPrice : null,
      invalidLinks: invalidLinks.length > 0 ? invalidLinks : null,
      refundLinkName,
      refundLinkCover,
    }
  }

  // 服务包购买后数据处理
  async completeService(insertData: any, user: any, params?: Params) {
    const dbSession = params?.mongoose?.session
    const options = dbSession ? {session: dbSession} : {}
    let {packId, order, total, price, giftCount, point, isPoint, session, isPromotion} = insertData
    let isNew = false
    let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId}, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      if (session) {
        buyData.session = session
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, params || {})
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }
    if (total > 0) {
      await this.app.service('service-pack-user-data').add(
        {
          packUser: packUser._id,
          type: payMethod,
          times: total,
          payMethod,
          isNew,
          order,
          isPromotion,
        },
        params || {}
      )
      isNew = false
    }
    if (giftCount) {
      await this.app.service('service-pack-user-data').add(
        {
          packUser: packUser._id,
          type: 'gift',
          times: giftCount,
          payMethod: 'gift',
          isNew,
          order,
          isPromotion,
        },
        params || {}
      )
    }
    return packUser
  }
  // 服务包购买后数据处理 代课服务 线下
  async completeServiceSubstitute(insertData: any, user: any, params?: Params) {
    const dbSession = params?.mogoose?.session
    const options = dbSession ? {session: dbSession} : {}
    let {packId, order, total, price, giftCount, point, isPoint, session, isPromotion, country, city, isOnCampus} = insertData
    let isNew = false
    let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId, pid: {$exists: false}}, null, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      if (session) {
        buyData.session = session
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, params || {})
    }
    if (isOnCampus) {
      var onCampusData: any = await this.app
        .service('service-pack-user')
        .substituteCreate({packId, pid: packUser._id, order, country, city, isPoint}, params || {})
      isNew = onCampusData.isNew
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }

    await this.app.service('service-pack-user-data').addSubstitute(
      {
        packUser: isOnCampus ? onCampusData.packUserSubstitute._id : packUser._id,
        isNew,
        order,
        payMethod,
        type: 'cash',
        times: Number(((total + giftCount) * 60).toFixed(0)),
      },
      params || {}
    )
    return packUser
  }
  // 主题服务包购买后数据处理
  async completeServicePremium(insertData: any, user: any, params?: Params) {
    const dbSession = params?.mongoose?.session
    const options = dbSession ? {session: dbSession} : {}
    let {packId, premium, order, total, price, giftCount, point, isPoint, session, isPromotion, packUserTasks = [], oldPackUser} = insertData
    let isNew = false
    let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId}, null, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, params || {})
    }

    let packUserPremium: any
    if (oldPackUser) {
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({_id: oldPackUser}, null, options)
    } else {
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({uid: user._id, pid: packUser._id.toString(), premium, order}, null, options)
    }
    if (!packUserPremium) {
      const pack: any = await this.app.service('service-pack').Model.findById(packId, null, options)
      await this.app.service('service-pack-user').contentOrientatedCreate(
        {
          order,
          pid: packUser._id,
          contentOrientated: pack.contentOrientated,
          isPoint,
          servicePremium: packId,
        },
        params || {}
      )
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({uid: user._id, pid: packUser._id.toString(), premium, order}, null, options)
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }
    if (total > 0) {
      let addData: any = {
        packUser: packUserPremium._id,
        type: payMethod,
        times: total,
        payMethod,
        isNew: true,
        order,
        isPromotion,
      }
      if (packUserTasks.length > 0) {
        addData.packUserTasks = packUserTasks
      }
      await this.app.service('service-pack-user-data').add(addData, params || {})
    }
    if (giftCount) {
      let addData: any = {
        packUser: packUserPremium._id,
        type: 'gift',
        times: giftCount,
        payMethod: 'gift',
        isNew: true,
        order,
        isPromotion,
      }
      if (packUserTasks.length > 0) {
        addData.packUserTasks = packUserTasks
      }
      await this.app.service('service-pack-user-data').add(addData, params || {})
    }
    return packUser
  }

  // 计算服务包使用数量,可退次数
  async calcServiceCount(packUser: any, orderId: any, serviceTicket: any = '', params?: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    let query: any = {
      packUser: packUser._id,
      order: orderId,
    }
    if (serviceTicket) {
      query.serviceTicket = serviceTicket
    }
    let packUserData = await this.app.service('service-pack-user-data').Model.find(query, null, options)
    let refundCount = 0
    let unused = 0
    let used = 0
    let unusedCash = 0
    let unusedPoint = 0
    let unusedGift = 0
    for (let i = 0; i < packUserData.length; i++) {
      const item: any = packUserData[i]
      if (item.status == 0) {
        unused++
        if (item.payMethod != 'gift') {
          refundCount++
        }
        if (item.payMethod == 'cash') {
          unusedCash++
        }
        if (item.payMethod == 'point') {
          unusedPoint++
        }
        if (item.payMethod == 'gift') {
          unusedGift++
        }
      } else {
        used++
      }
    }

    return {
      refundCount,
      unused,
      used,
      unusedCash,
      unusedPoint,
      unusedGift,
    }
  }
  // 计算服务包使用数量,可退次数 代课
  async calcServiceCountSubstitute(packUser: any, link: any, isPoint: boolean) {
    let remaining = packUser.total - packUser.used
    let {giftCount, count} = link
    giftCount = giftCount * 60
    count = count * 60
    let refundCount = 0
    let unused = 0
    let used = 0
    let unusedCash = 0
    let unusedPoint = 0
    let unusedGift = 0
    if (remaining >= count + giftCount) {
      refundCount = count
      unused = count + giftCount
      used = 0
      unusedCash = isPoint ? 0 : refundCount
      unusedPoint = isPoint ? refundCount : 0
      unusedGift = giftCount
    } else if (remaining < count + giftCount) {
      refundCount = remaining - giftCount > 0 ? remaining - giftCount : 0
      unused = remaining
      used = count + giftCount - remaining
      unusedCash = isPoint ? 0 : refundCount
      unusedPoint = isPoint ? refundCount : 0
      unusedGift = remaining - refundCount
    }
    return {
      refundCount,
      unused,
      used,
      unusedCash,
      unusedPoint,
      unusedGift,
    }
  }
  // 判断是否已有Promotion
  async checkPromotionExist(buyer: string, linkId: string, params?: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    // let exist = await this.Model.find({
    //   buyer: buyer,
    //   status: {$in: [100, 200]},
    //   links: {$elemMatch: {id: linkId, promotion: true, removed: {$exists: false}}},
    // })
    let exist = await this.Model.find(
      {
        buyer: buyer,
        links: {$elemMatch: {id: linkId, promotion: true}},
      },
      null,
      options
    )
    return exist.length > 0
  }
  // 一个月内购买了几次Promotion
  async getCountPromotionByMonth({buyer}: any) {
    let exist = await this.Model.find({
      buyer: buyer,
      status: {$in: [100, 200]},
      links: {$elemMatch: {promotion: true, removed: {$exists: false}}},
      createdAt: {
        $gt: new Date(new Date().setMonth(new Date().getMonth() - 1)),
        $lt: new Date(),
      },
    })
    return {count: exist.length}
  }
  // 该用户已购买的Promotion服务包id 替代users.freeServiceType
  async getPromotionServiceId({buyer}: any) {
    let orders = await this.Model.find({
      buyer: buyer,
      status: {$in: [100, 200]},
      links: {$elemMatch: {promotion: true, removed: {$exists: false}}},
    })
    let freeServiceType: any = {}
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      if (order.servicePremium) {
        freeServiceType[order.servicePremium] = order._id
      } else {
        for (let j = 0; j < order.links.length; j++) {
          const link: any = order.links[j]
          if (link.promotion) {
            freeServiceType[link.id] = order._id
          }
        }
      }
    }
    return {freeServiceType}
  }
  /**
   * 查询已购买过的service,用于过滤领取免费服务包和session捆绑服务包.
   * 替代getPromotionServiceId部分场景
   */
  async getOrderedService({buyer}: any) {
    let orders = await this.Model.find({buyer: buyer, status: {$ne: 400}, 'links.style': {$in: ['service', 'service_premium']}})
    let serviceIds: any = new Set()
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      for (let j = 0; j < order.links.length; j++) {
        const link: any = order.links[j]
        if (link.style == 'service' || link.style == 'service_premium') {
          serviceIds.add(link.id)
        }
      }
    }
    return {serviceIds: Array.from(serviceIds)}
  }
  // 根据booking查询订单
  async getPromotionByBooking({booking}: any) {
    let bookingInfo: any = await this.app.service('service-booking').Model.findOne({_id: booking})
    let {packUserData} = bookingInfo
    let userDataInfo = await this.app.service('service-pack-user-data').Model.find({_id: {$in: packUserData}})
    let orderIds = userDataInfo.map((e: any) => e.order)
    let orders = await this.Model.find({_id: {$in: orderIds}})

    let freeServiceType: any = {}
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      if (order.servicePremium) {
        freeServiceType[order.servicePremium] = order._id
      } else {
        for (let j = 0; j < order.links.length; j++) {
          const link: any = order.links[j]
          if (link.promotion) {
            freeServiceType[link.id] = order._id
          }
        }
      }
    }
    return {freeServiceType}
  }
  // 认证精品课快照 未使用查询 (弃用 改为直接find查询)
  // async getPremiumCloudUnused({}: any, params: Params) {
  //   const buyer = params?.user?._id
  //   let list: any = await this.Model.find({buyer, 'links.style': 'premium_cloud', 'links.premiumCloudUnused': true}).lean()
  //   let unused = []
  //   for (let i = 0; i < list.length; i++) {
  //     let {links} = list[i]
  //     for (let j = 0; j < links.length; j++) {
  //       let link = links[j]
  //       if (link.style == 'premium_cloud' && link.premiumCloudUnused) {
  //         link.serviceAuth = link.id
  //         unused.push(link)
  //       }
  //     }
  //   }
  //   return unused
  // }

  // 未支付提醒,超时前15min
  unpaidRemind() {
    this.Model.find({status: 100, reminder: {$lt: 1}, expiration: {$lt: Date.now() + 15 * 60 * 1000, $gt: Date.now() + 1 * 60 * 1000}}).then(
      async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const order = rs[i]
          let {links} = order
          const user = await this.app.service('users').uidToInfo(order.buyer)
          this.app.service('notice-tpl').send(
            'OrderUnpaidRemind',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              url: `${SiteUrl}/v2/order/detail/${order._id}`,
              name: `${links[0].name}${links.length > 1 ? ' +' + (links.length - 1) : ''}`,
            }
          )
          await this.Model.updateOne({_id: order._id}, {$set: {reminder: 1}})
        }
      }
    )
  }
  // 超时自动取消
  timeoutClose() {
    this.Model.find({status: 100, expiration: {$lt: Date.now()}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const order = rs[i]
        let links = order.links.map((e: any) => {
          e.removed = true
          return e
        })
        await this.Model.updateOne({_id: order._id}, {status: 400, links})

        const user = await this.app.service('users').uidToInfo(order.buyer)
        let url = `${SiteUrl}/v2/order/detail/${order._id}`
        this.app
          .service('notice-tpl')
          .send(
            'OrderTimeout',
            {_id: user._id, email: user.email},
            {url, nickname: user.nickname, name: `${order.links[0].name}${order.links.length > 1 ? ' +' + (order.links.length - 1) : ''}`}
          )
      }
    })
  }
  // braintree订单settled查询处理
  paymentConfirm(params: any) {
    this.Model.find({status: {$in: [200, 500, 501, 502, 503]}, settled: false, payMethod: {$in: ['braintree']}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const order = rs[i]

        let detail: any = await this.app.service('braintree').get('detail', {query: {id: order.braintreeId}})
        if (detail.status === 'settled') {
          if (order.refund.length > 0) {
            await this.app.service('order').dispatchRefund(order, params)
          }
          this.app.service('order').patch(order._id, {settled: true})
        }
      }
    })
  }

  // 积分/佣金分账结算
  // type: {type: String, enum: ['unit', 'session_public', 'session_self_study', 'session_service_pack', 'service_pack']},
  autoSeparate() {
    this.Model.find({status: {$in: [200, 500, 501, 502, 503]}, settled: true, isSeparated: false, $or: [{isSchool: false}, {isSchool: {$exists: false}}]}).then(
      async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const order = rs[i]
          if (order.status == 200) {
            let {separateAllowed} = await this.getOrderRefundCheck({id: order._id, status: 500})
            if (!separateAllowed) {
              continue
            }
          }
          await this.settleByOrder({oid: order._id, status: 1})
          await this.Model.updateOne({_id: order._id}, {$set: {isSeparated: true}})
        }
      }
    )
  }

  async cron1({}: any, params?: Params): Promise<any> {
    this.unpaidRemind()
    this.timeoutClose()
    this.paymentConfirm(params)
    this.autoSeparate()
    this.orderRecovery.retryFailedRefunds()
  }

  // 执行一次 links.gift=true 更新为 links.promotion=true
  async getUpdateGiftToPromotion() {
    let order = await this.Model.find({'links.gift': true})
    for (let i = 0; i < order.length; i++) {
      const item: any = order[i]
      let links = item.links.map((e: any) => {
        if (e.gift) {
          e.promotion = true
        }
        return e
      })
      await this.Model.updateOne({_id: item._id}, {links})
    }
  }

  // 去除逻辑  更新为谁分享的就给谁结算,和跟踪列表无关
  // async handleInviter({inviter, buyer, servicePack}: any) {
  //   let user: any = await this.app.service('users').Model.findOne({inviteCode: inviter})
  //   let servicerConfData: any = await this.app.service('service-conf').Model.findById(user._id)
  //   let {managerRoles = []} = user
  //   if (managerRoles.includes('sales') || managerRoles.includes('sales_manager') || servicerConfData?.serviceRoles?.includes('consultant')) {
  //     let salesFollowData: any = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack})
  //     // let salesFollowData: any = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack, type: 'following'})
  //     let inviterUid = salesFollowData ? salesFollowData.sales : ''
  //     if (inviterUid) {
  //       let inviterUser: any = await this.app.service('users').Model.findOne({_id: inviterUid})
  //       return inviterUser.inviteCode
  //     } else {
  //       return inviter
  //     }
  //   } else {
  //     return inviter
  //   }
  // }

  // prompt,premium_cloud使用 更新used
  async getUsePrompt({orderId, prompt}: any) {
    return await this.Model.updateOne({_id: orderId, links: {$elemMatch: {id: prompt, used: false}}}, {$set: {'links.$.used': true}})
  }
  // 判断订单type
  async getOrderType({links, servicePremium}: any) {
    let type = ''
    let hasUnit = links.some((item: any) => item.style === 'unit')
    let hasSession = links.some((item: any) => item.style === 'session')
    let hasSelfStudy = links.some((item: any) => item.goods.type === 'selfStudy')
    let hasService = links.some((item: any) => item.style === 'service')
    let hasServiceSubstitute = links.some((item: any) => item.style === 'service_substitute')
    let hasPrompt = links.some((item: any) => item.style === 'prompt')
    let hasPremiumCloud = links.some((item: any) => item.style === 'premium_cloud')
    if (hasUnit) {
      type = 'unit'
    }
    if (hasSession && !hasService && !hasSelfStudy) {
      type = 'session_public'
    }
    if (hasSession && !hasService && hasSelfStudy) {
      type = 'session_self_study'
    }
    if (hasService && !hasSession) {
      type = 'service_pack'
    }
    if (hasServiceSubstitute && !hasSession) {
      type = 'service_substitute'
    }
    if (hasService && hasSession) {
      type = 'session_service_pack'
    }
    if (servicePremium) {
      type = 'service_premium'
    }
    if (hasPremiumCloud) {
      type = 'premium_cloud'
    }
    if (hasPrompt) {
      type = 'prompt'
    }
    return type
  }

  addLogStorage({logs, item}: any) {
    if (!logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`]) {
      logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`] = item
    } else {
      logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`].change += item.change
    }
  }
  // 结算订单的积分/佣金
  async settleByOrder({oid, status = 1}: any) {
    let order: any = await this.app.service('order').Model.findById(oid)
    if (!order) {
      return
    }
    if (order.price == 0 && order.point == 0) {
      return
    }
    let {_id, buyer, type, links, servicePremium, isPoint, schoolInviter, inviteSource, inviteSourceId} = order

    let logs: any = {}
    for (let j = 0; j < links.length; j++) {
      const goods = links[j]
      let remainingPrice = Number((goods.price - goods.refundPrice).toFixed(0))
      let remainingPoint = Number((goods.point - goods.refundPoint).toFixed(0))

      if (remainingPrice <= 0 && remainingPoint <= 0) {
        continue
      }

      // 商品作者
      let authorUid
      if (goods.style == 'unit') {
        // let unitData: any = await this.app.service('unit').Model.findOne({_id: goods.id})
        let unitData: any = goods.goods
        authorUid = unitData.uid
      } else if (goods.style == 'session') {
        // let sessionData: any = await this.app.service('session').Model.findOne({_id: goods.id})
        let sessionData: any = goods.goods
        authorUid = sessionData.uid
      }
      // 积分购买 结算积分 给unit/session原作者
      if (isPoint) {
        if (goods.style != 'unit' && goods.style != 'session') {
          continue
        }
        let resPoint = await this.app
          .service('point-setting')
          .calcPoint({type: {category: 'points_purchase'}, amount: remainingPoint, tab: 'earn', isPoint: false})

        this.addLogStorage({
          logs,
          item: {
            uid: authorUid,
            tab: 'earn',
            category: 'points_purchase',
            change: resPoint.point,
            businessId: _id,
            snapshot: order,
            type: 'point',
            isSchool: false,
          },
        })
      } else {
        // 现金够买
        let inviteCode = goods.inviter || order.inviter
        let schoolInviter = goods.schoolInviter || order.schoolInviter
        let inviteSource = goods.inviteSource || order.inviteSource
        let inviteSourceId = goods.inviteSourceId || order.inviteSourceId
        if (!inviteCode) {
          continue
        }
        let inviteUser: any = await this.app.service('users').Model.findOne({inviteCode})

        if (!inviteUser) {
          // 邀请码匹配不到人 直接跳过
          await this.Model.updateOne({_id: _id}, {$set: {isSeparated: true}})
          continue
        }

        // 分享人为商品作者,不结算
        if (inviteUser._id == authorUid) {
          continue
        }

        let isCommission = false
        let commissionRole = '' // commission-setting.role
        let isAgent = await this.app.service('manager').isAgent(inviteUser._id)
        if (isAgent) {
          commissionRole = 'agency'
        }
        if (inviteSource == 'sales_follow_up') {
          // 销售跟踪 从销售跟踪分享的 且当前仍在跟踪
          let salesFollowData: any
          if (type == 'service_premium') {
            salesFollowData = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack: servicePremium})
          } else if (goods.style == 'service' || goods.style == 'service_substitute') {
            salesFollowData = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack: goods.id})
          }
          let inviterUid = salesFollowData ? salesFollowData.sales : ''
          if (inviterUid && inviterUid == inviteUser._id) {
            isCommission = true
            commissionRole = salesFollowData.salesType == 'manager' ? 'classcipe_staff' : 'education_consultant'
          }
        }
        if (inviteSource == 'new_prompt') {
          let sourceSession: any = await this.app.service('session').Model.findOne({_id: inviteSourceId})
          // 个人workshop
          if (sourceSession.personal && sourceSession?.students.length == 0) {
            isCommission = true
          }
          // 学校课堂
          if (!sourceSession.personal) {
            schoolInviter = sourceSession.school
          }
        }
        if (schoolInviter) {
          commissionRole = 'organization'
        }
        if (isAgent || schoolInviter || isCommission) {
          // 结算佣金
          let category = goods.style
          if (order.type == 'session_self_study' && goods.style == 'session') {
            category = 'self_study'
          }
          let resCommission = await this.app
            .service('commission-setting')
            .calcCommission({type: {category}, amount: remainingPrice, tab: 'earn', role: commissionRole})
          this.addLogStorage({
            logs,
            item: {
              uid: schoolInviter || inviteUser._id,
              tab: 'earn',
              source: 'reward',
              category: category,
              change: resCommission.commission,
              businessId: _id,
              snapshot: order,
              type: 'commission',
              isSchool: schoolInviter ? true : false,
              role: commissionRole,
            },
          })
          if (schoolInviter) {
            // 学校分享 给学校佣金 个人积分
            let resPoint = await this.app.service('point-setting').calcPoint({type: {category}, amount: remainingPrice, tab: 'earn', isPoint: false})
            this.addLogStorage({
              logs,
              item: {
                uid: inviteUser._id,
                tab: 'earn',
                source: 'reward',
                category: category,
                change: resPoint.point,
                businessId: _id,
                snapshot: order,
                type: 'point',
                isSchool: false,
              },
            })
          }
        } else {
          // 结算积分
          let category = goods.style
          if (order.type == 'session_self_study' && goods.style == 'session') {
            category = 'self_study'
          }
          let resPoint = await this.app.service('point-setting').calcPoint({type: {category}, amount: remainingPrice, tab: 'earn', isPoint: false})

          if (resPoint.success) {
            this.addLogStorage({
              logs,
              item: {
                uid: inviteUser._id,
                tab: 'earn',
                source: 'reward',
                category: category,
                change: resPoint.point,
                businessId: _id,
                snapshot: order,
                type: 'point',
                isSchool: false,
              },
            })
          }
        }
      }
    }

    for (let key in logs) {
      await this.app.service('point-log').handleAddLog({
        ...logs[key],
        status,
      })
    }
  }
}
