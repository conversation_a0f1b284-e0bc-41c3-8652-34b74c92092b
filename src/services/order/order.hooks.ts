import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')
import {ObjectID} from 'bson'

// Don't remove this comment. It's needed to format import lines nicely.

import hook from '../../hook'
const {authenticate} = authentication.hooks

const inviteExpiredYear = 1 //拉新后有效期

export default {
  before: {
    all: [authenticate('jwt')],
    find: [
      hook.userQuery('buyer'),
      async (d: HookContext) => {
        if (!d.params.query?.$skipSys) {
          hook.sysQuery('buyer', true, ['sales', 'customer_service', 'sales_manager', 'customer_service_manager', 'accountant', 'admin'])(d)
        } else {
          delete d.params.query.$sys
          delete d.params.query.$skipSys
        }
      },
    ],
    get: [hook.toClass],
    create: [
      async (d: HookContext) => {
        const {
          link,
          cart,
          promotion = false,
          inviter,
          isPoint = false,
          sharedSchool,
          isSchool = false,
          school,
          persons = 1,
          servicePremium,
          servicePackApply,
          noDiscount = false,
          freeServiceClaimed = false,
        } = d.data
        const user: any = d.params.user
        const buyer = isSchool ? school : user._id
        if (isSchool) {
          d.data.schoolAdmin = user._id
        }
        const _id = new ObjectID()

        if (isPoint && isSchool) {
          return Promise.reject(new GeneralError({message: 'School can not use point'}))
        }
        if (!isSchool && persons != 1) {
          return Promise.reject(new GeneralError({message: 'Personal cannot buy multiple copies'}))
        }
        // 主题服务包可销售目标判断
        if (servicePremium) {
          var servicePremiumData = await d.app.service('service-pack').Model.findOne({_id: servicePremium}).lean()
          d.data.servicePremiumSnapshot = servicePremiumData
          if (servicePremiumData.salesTarget.indexOf('personal') === -1 && !isSchool && !sharedSchool) {
            return Promise.reject(new GeneralError({message: 'The service pack is not for personal'}))
          }
          if (servicePremiumData.salesTarget.indexOf('school') === -1 && isSchool) {
            return Promise.reject(new GeneralError({message: 'The service pack is not for school'}))
          }
          if (!isSchool) {
            // 申请审核判断
            let applyData: any = await d.app.service('service-pack-apply').Model.findOne({uid: buyer, sharedSchool, servicePack: servicePremium})
            if (applyData?.status != 1 && !freeServiceClaimed) {
              return Promise.reject(new GeneralError({message: 'Apply has not been approved'}))
            }
            // 可购买过期判断
            if (sharedSchool) {
              if (applyData?.purchaseExpireAt < new Date()) {
                return Promise.reject(new GeneralError({message: 'Purchase has expired'}))
              }
            }
          }
        }

        let links = []
        let sellers: any = []
        let price = 0
        let point = 0
        let cartData = []
        let cartDict: any = {}
        if (cart) {
          cartData = await d.app.service('cart').Model.find({_id: {$in: cart}})
        }
        for (let i = 0; i < cartData.length; i++) {
          cartDict[cartData[i].goodsId] = cartData[i]
        }

        d.data.buyer = buyer
        d.data.paid = 0
        if (link instanceof Array) {
          links = link
        } else {
          links.push(link)
        }

        if (links.length === 0) {
          return Promise.reject(new GeneralError({message: 'Link can not be empty'}))
        }
        // check unpaid order exists
        if (servicePremium) {
          let unpaidOrder = await d.service.Model.find({
            buyer: buyer,
            status: 100,
            sharedSchool,
            servicePremium,
          })
          if (unpaidOrder.length > 0) {
            return Promise.reject(new GeneralError({message: 'Link has unpaid order', order: unpaidOrder}))
          }
        } else {
          let unpaidLinkIds = links.map((item: any) => item.id)
          let unpaidOrder = await d.service.Model.find({
            buyer: buyer,
            status: 100,
            links: {$elemMatch: {id: {$in: unpaidLinkIds}, removed: {$exists: false}}},
            type: {$ne: 'service_premium'},
          })
          if (unpaidOrder.length > 0) {
            return Promise.reject(new GeneralError({message: 'Link has unpaid order', order: unpaidOrder}))
          }
          // check order exists
          let linkIds = links.filter((item: any) => item.style === 'unit' || item.style === 'session').map((item: any) => item.id)
          let order = await d.service.Model.find({
            buyer: buyer,
            status: {$in: [200]},
            links: {$elemMatch: {id: {$in: linkIds}, removed: {$exists: false}}},
            type: {$ne: 'service_premium'},
          })
          if (order.length > 0) {
            return Promise.reject(new GeneralError({message: 'Link has been ordered', order: order}))
          }
        }

        let dict: any = {}
        let serviceSessionId = ''
        // 判断商品存在可购买,计算价格
        for (let i = 0; i < links.length; i++) {
          const item = links[i]
          let discount
          let goods
          if (cartDict[item.id]) {
            item.inviter = cartDict[item.id].inviter
            item.schoolInviter = cartDict[item.id].schoolInviter
            item.inviteSource = cartDict[item.id].inviteSource
            item.inviteSourceId = cartDict[item.id].inviteSourceId
          }
          try {
            if (item.style === 'unit') {
              if (isSchool) {
                return Promise.reject(new GeneralError({message: 'School can not buy unit'}))
              }
              goods = await d.app.service('unit').ext(await d.app.service('unit').Model.findOne({_id: item.id}).lean())
            } else if (item.style === 'session') {
              if (isSchool) {
                return Promise.reject(new GeneralError({message: 'School can not buy session'}))
              }
              goods = await d.app.service('session').Model.findOne({_id: item.id}).lean()
              serviceSessionId = item.id
            } else if (item.style === 'service' || item.style === 'service_substitute') {
              goods = await d.app.service('service-pack').Model.findOne({_id: item.id}).lean()
              if (isSchool) {
                d.data.isTicket = true
              }
            } else if (item.style === 'service_premium') {
              if (!item.packUserTasks || item.packUserTasks.length == 0) {
                let auth = await d.app.service('service-auth').Model.findOne({_id: item.id})
                let packUserTasks = []
                for (let key in auth.linkSnapshot) {
                  packUserTasks.push(auth.linkSnapshot[key]._id)
                }
                item.packUserTasks = packUserTasks
              }
              goods = servicePremiumData
            } else if (item.style === 'premium_cloud') {
              let auth = await d.app.service('service-auth').get(item.id)
              goods = auth
            } else if (item.style === 'prompt') {
              goods = await d.app.service('prompts').Model.findOne({_id: item.id})
            }
          } catch (error) {
            goods = false
          }
          if (
            !goods ||
            (item.style === 'unit' && !goods.publish.lib) ||
            (item.style === 'service' && !goods.status) ||
            (item.style === 'service_substitute' && !goods.status) ||
            (servicePremium && !servicePremiumData.status)
          ) {
            return Promise.reject(new GeneralError({message: 'Link not found or unpublish', goods: item}))
          }
          if (item.style === 'session' && goods.type !== 'selfStudy' && Date.now() > new Date(goods.regDate).getTime()) {
            return Promise.reject(new GeneralError({message: 'Session registration time has passed', goods: item}))
          }
          dict[item.id] = goods
          discount = goods.discount

          let calcResult
          if (item.style === 'service' || item.style === 'service_substitute') {
            if (promotion) {
              calcResult = {
                success: true,
                price: 0,
                point: 0,
              }
              item.count = 0
              item.promotion = true
              item.giftCount = 1
              item.persons = 1

              let promotionExist = await d.service.checkPromotionExist(buyer, item.id)
              if (promotionExist) {
                links.splice(i, 1)
                i--
                continue
              }
            } else {
              let unitPrice = goods.price
              if (item.isOnCampus) {
                discount = goods.onCampusPrice.find((e: any) => item.city === e.city)
                unitPrice = discount.price
                discount = discount.discount
              }
              calcResult = await d.service.calcPriceService({
                discount,
                unitPrice: unitPrice,
                count: item.count,
                discountConfig: goods.discountConfig,
                isPoint,
                persons,
                noDiscount,
                style: item.style,
              })
              item.giftCount = calcResult.giftCount
              item.persons = persons
            }
          } else if (item.style === 'service_premium') {
            if (promotion) {
              calcResult = {
                success: true,
                price: 0,
                point: 0,
              }
              item.count = 0
              item.promotion = true
              item.giftCount = 1

              let promotionExist = await d.service.checkPromotionExist(buyer, item.id)
              if (promotionExist) {
                links.splice(i, 1)
                i--
                continue
              }
            } else {
              calcResult = await d.service.calcPriceServicePremium({
                premium: item.id,
                goods,
                sharedSchool,
                isPoint,
                isSchool,
                count: item.count,
                noDiscount,
                buyer,
              })
              item.count = calcResult.count
            }
          } else if (item.style === 'premium_cloud') {
            calcResult = {
              success: true,
              price: 50 * goods.unitSnapshot.questions.length,
            }
          } else if (item.style === 'prompt') {
            let incomeSetting = await d.app.service('income-setting').Model.findOne({tab: 'claim', category: 'prompt'})
            if (incomeSetting) {
              calcResult = {
                success: true,
                price: Number((incomeSetting.value * goods.questions.length).toFixed(0)),
              }
            } else {
              calcResult = {
                success: true,
                price: 0,
              }
            }
            // 1v1课程前10张免费 免费不走订单
            // let sessionData = await d.app.service('session').Model.findOne({_id: item.sessionId}).lean()
            // if (sessionData.booking) {
            //   let freeCount = await d.service.Model.find({buyer, status: 200, links: {$elemMatch: {id: item.id, price: 0}}})
            //   if (freeCount.length < 10) {
            //     calcResult.price = 0
            //   }
            // }
          } else {
            calcResult = await d.service.calcPrice(discount, isPoint, goods.type == 'selfStudy' ? 'self_study' : item.style, goods)
          }
          if (!calcResult.success) {
            return Promise.reject(new GeneralError({message: calcResult.message}))
          }
          if (isPoint) {
            item.point = calcResult.point || 0
            point += calcResult.point
          } else {
            item.price = calcResult.price || 0
            price += calcResult.price
          }
          item.goods = Acan.clone(goods)
        }
        if (links.length === 0) {
          return Promise.reject(new GeneralError({message: 'Link can not be empty'}))
        }
        d.data.price = price
        d.data.point = point

        // TODO gift card
        if (d.data.point > 0) {
          // 积分支付
          if (d.data.point > user.point) {
            return Promise.reject(new GeneralError({message: 'Insufficient points'}))
          }
          d.data.paid = 1
          d.data.payMethod = ['point']
          d.data.status = 200
          d.data.settled = true
        } else {
          // 现金支付
          if (d.data.price === 0) {
            d.data.status = 200
            d.data.settled = true
            d.data.paidAt = new Date()
          } else {
            d.data.status = 100
          }
        }

        // 生成订单type
        d.data.type = await d.service.getOrderType({links, servicePremium})

        // 完善links信息,处理无需支付订单
        for (let i = 0; i < links.length; i++) {
          const item = links[i]
          if (Acan.isObjectId(item.id)) {
            if (item.style === 'unit') {
              if (d.data.status === 200) {
                let post = await d.app.service('unit').copy({_id: item.id, orderId: _id.toString()}, d.params)
                item.name = post.name
                item.cover = post.cover
                item.newId = post._id
                item.hash = post.sid
                if (!sellers.includes(post.sourceUid)) {
                  sellers.push(post.sourceUid)
                }
              } else {
                let post = dict[item.id]
                item.name = post.name
                item.cover = post.cover
                if (!sellers.includes(post.uid)) {
                  sellers.push(post.uid)
                }
              }
            } else if (item.style === 'session') {
              let post = dict[item.id]
              item.name = post.name
              item.cover = post.image
              if (!sellers.includes(post.uid)) {
                sellers.push(post.uid)
              }
              if (d.data.status === 200) {
                const reg = {avatar: user.avatar, nickname: await d.app.service('users').nameFormatter({user}), _id: user._id, order: _id.toString()}
                d.app.service('session').patch(item.id, {_date: new Date(post.start).toString(), $addToSet: {reg}}, {user})
              }
            } else if (item.style === 'service' || item.style === 'service_substitute') {
              let post = dict[item.id]
              item.name = post.name
              item.cover = post.cover
              item.sessionId = serviceSessionId
              if (d.data.status === 200) {
                let insertData: any = {
                  packId: item.id,
                  order: _id.toString(),
                  total: item.count,
                  price: item.price,
                  // gift: item.promotion ? true : false,
                  giftCount: item.giftCount,
                  point: item.point,
                  isPoint: isPoint,
                  isPromotion: item.promotion,
                }
                if (serviceSessionId) {
                  insertData.session = serviceSessionId
                }

                // session捆包服务包类型订单 需判定session是否promotion 是:服务包次数都为gift,否:正常加次数(cash/point)
                if (d.data.type === 'session_service_pack') {
                  let sessionDoc: any = await d.app.service('session').Model.findOne({_id: item.sessionId})
                  if (sessionDoc.promotion) {
                    insertData.giftCount = item.count + item.giftCount
                    insertData.total = 0
                  }
                }
                if (item.style === 'service_substitute') {
                  if (item.isOnCampus) {
                    insertData.country = item.country
                    insertData.city = item.city
                  }
                  insertData.isOnCampus = item.isOnCampus
                  await d.service.completeServiceSubstitute(insertData, {_id: buyer}, d.params)
                } else {
                  if (!isSchool) {
                    await d.service.completeService(insertData, user, d.params)
                  }
                }
              }
            } else if (item.style === 'service_premium') {
              let post = dict[item.id]
              item.name = post.name
              item.cover = post.cover
              if (!sellers.includes(post.uid)) {
                sellers.push(post.uid)
              }
              if (d.data.status === 200) {
                let insertData: any = {
                  premium: item.id,
                  packId: servicePremium,
                  order: _id.toString(),
                  total: item.count,
                  price: item.price,
                  // gift: item.promotion ? true : false,
                  giftCount: item.giftCount,
                  point: item.point,
                  isPoint: isPoint,
                  isPromotion: item.promotion,
                  packUserTasks: item.packUserTasks,
                }
                if (item.oldPackUser) {
                  insertData.oldPackUser = item.oldPackUser
                }
                if (!isSchool) {
                  await d.service.completeServicePremium(insertData, user, d.params)
                } else {
                  await d.service.completeServicePremium(insertData, {_id: school}, d.params)
                }
              }
            } else if (item.style === 'premium_cloud') {
              if (d.data.status === 200) {
                let sessionDoc = await d.app
                  .service('service-booking')
                  .importByBooking({serviceAuthId: item.id, bookingId: item.bookingId, order: _id.toString()}, d.params)
                item.session = sessionDoc._id
                item.used = true
              }
            } else if (item.style === 'prompt') {
              // if (d.data.status === 200) {
              //   await d.app.service('users').Model.updateOne({_id: buyer}, {$inc: {freePromptCount: 1}})
              // }
              let post = dict[item.id]
              if (!sellers.includes(post.uid)) {
                sellers.push(post.uid)
              }
            }
          }
        }
        if (servicePackApply && d.data.status === 200) {
          d.app.service('service-pack-apply').Model.updateOne({_id: servicePackApply}, {interviewOrder: _id.toString()}).exec()
        }
        if (servicePremium && d.data.status === 200) {
          d.app.service('service-pack-apply').updateOrderInfo({uid: buyer, servicePack: servicePremium, sharedSchool, order: _id.toString()})
        }
        if (d.data.isTicket && d.data.status === 200) {
          d.app.service('service-pack-ticket').generate({persons, school, servicePremium, order: _id.toString(), links})
        }

        if (cart) {
          await d.app.service('cart').Model.deleteMany({_id: {$in: cart}})
        }

        if (inviter) {
          if (d.data.type == 'service_premium') {
            // d.data.inviter = await d.service.handleInviter({inviter, buyer, servicePack: servicePremium})
          } else if (d.data.type == 'service_pack' || d.data.type == 'session_service_pack') {
            let serviceId = links.find((e: any) => e.style === 'service' || e.style === 'service_substitute').id
            // d.data.inviter = await d.service.handleInviter({inviter, buyer, servicePack: serviceId})
          } else {
            d.data.inviter = inviter
          }
        } else {
          if (!isSchool) {
            let buyerInfo = await d.app.service('users').Model.findOne({_id: buyer})
            let isExpiration = new Date(buyerInfo.createdAt).getTime() + inviteExpiredYear * 365 * 24 * 3600 * 1000 < Date.now()
            if (buyerInfo.inviter && !isExpiration) {
              d.data.inviter = buyerInfo.inviter
            }
          }
        }
        d.data.no = Acan.MD5(new Date().getTime() + '' + Math.floor(Math.random() * 1000000)).substring(0, 12)
        d.data.sellers = sellers
        d.data.links = links
        d.data._id = _id
        d.data.isSeparated = false
        delete d.data.link
      },
    ],
    update: [hook.disable],
    patch: [
      async (d: HookContext) => {
        if (d.params.provider) {
          return Promise.reject(new GeneralError('Not allowed'))
        }
      },
    ],
    remove: [hook.disable],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        if (d.result) {
          for (let i = 0; i < d.result.data.length; i++) {
            await d.service.extBuyer(d.result.data[i])
          }
        }
      },
    ],
    get: [
      async (d: HookContext) => {
        if (d.result.buyer) {
          await d.service.extBuyer(d.result)
        }
      },
    ],
    create: [
      async (d: HookContext) => {
        let {freeServiceClaimed} = d.data
        let {_id, status, isPoint, buyer, point} = d.result

        if (freeServiceClaimed) {
          await d.app.service('users').Model.updateOne({_id: buyer}, {freeServiceClaimed: true})
        }
        if (status === 100) {
          await d.service.patch(d.result._id, {expiration: await d.service.expirationDate(d.result)})
        }
        if (isPoint) {
          d.app.service('point-log').getAddLog({
            uid: buyer,
            tab: 'claim',
            source: 'order',
            category: 'order',
            change: point * -1,
            businessId: _id.toString(),
            snapshot: d.result,
          })
        }
      },
    ],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
