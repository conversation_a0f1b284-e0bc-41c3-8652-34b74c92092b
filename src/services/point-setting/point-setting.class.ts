import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class PointSetting extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  /**
   * 获得/使用积分数量计算
   * amount 积分/美分
   * isPoint 传入的amount是否为积分
   */
  async calcPoint({type, amount, tab = 'claim', isPoint = false}: any, params?: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    let setting: any = await this.Model.findOne({...type, tab}, null, options)
    if (!setting) {
      return {
        success: false,
        message: 'No point setting',
        price: amount,
        point: 0,
      }
    }
    if (setting.mode === 'fixed') {
      return {
        success: true,
        price: 0,
        point: setting.value,
      }
    } else {
      let point = 0
      let num = (setting.value / 100) * (amount / (isPoint ? 1 : 100))
      if (!num) {
        point = 0
      } else if (num <= 1) {
        point = 1
      } else {
        point = Math.floor(num)
      }
      return {
        success: true,
        price: 0,
        point: point,
      }
    }
  }
}
