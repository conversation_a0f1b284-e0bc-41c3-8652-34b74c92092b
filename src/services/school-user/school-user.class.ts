import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'
const {GeneralError} = require('@feathersjs/errors')

import {FeathersError} from '@feathersjs/errors'
class PlanLimit extends FeathersError {
  constructor(message: string, name?: string, data?: any) {
    super(message, name || 'PlanLimit', 451, 'PlanLimit', data)
  }
}

export class SchoolUser extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  nameFormat(o: any) {
    if (!o.name) return
    o.name.map((v: string, i: number) => {
      o.name[i] = v.toFirstUpperCase()
    })
    o.nickname = o.name.join(' ')
    return o
  }
  async count({school}: any, params: Params) {
    const count = await this.Model.count({school, del: false})
    await this.app.service('school-plan').Model.updateOne({_id: school}, {'count.teacher': count})
  }
  async countClass({class: classes}: any, params: Params) {
    if (Acan.isEmpty(classes)) return
    if (!Array.isArray(classes)) classes = [classes]
    for (const classId of classes) {
      if (classId != 'cloudRoom') {
        const count = await this.Model.count({class: classId, del: false})
        await this.app.service('classes').Model.updateOne({_id: classId}, {'count.teacher': count})
      }
    }
  }
  async cleanUserSession({uid, school, classId}: {uid: string; school?: string; classId?: string}, params: Params): Promise<any> {
    // logger.warn('cleanUserSession', uid, school, classId)
    if ((!classId && !school) || !uid) return logger.warn('no uid or no school')
    const con: any = {del: false, status: {$ne: 'close'}, start: {$gte: new Date()}, uid}
    if (school) con.school = school
    if (classId) con.classId = classId
    logger.warn('clean user scheduled session', con)
    const sessionModel = this.app.service('session').Model
    const list = await sessionModel.find(con).select(['name'])
    logger.warn('need clean session:', list)
    for (const one of list) {
      await this.app.service('session').Model.deleteOne({_id: one._id.toString()})
    }
    return {}
  }
  getInfo({school, email}: {school: string; email: string}, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    return this.app.get('redisHCache')('schoolUser:Info' + school, email, async () => {
      const rs: any = Acan.clone(await this.Model.findOne({school, email}, null, options).select(['nickname', 'name', 'avatar', 'email', 'dob']))
      if (!rs) return {}
      rs.nickname = rs.name.join(' ')
      if (!rs.avatar) rs.avatar = ''
      rs.isSchool = true
      return rs
    })
  }
  async getLinkKey({school}: {school: string}, params: Params): Promise<any> {
    const key = await this.app.get('redis').HGET('schoolInviteKey', school)
    if (key) return key
    return await this.getResetLink({school}, params)
  }
  async getLinkInfo({key}: {key: string}, params: Params): Promise<any> {
    return JSON.parse(await this.app.get('redis').HGET('schoolUserInvite', key))
  }
  async getResetLink({school}: any, params: Params): Promise<any> {
    const redis = this.app.get('redis')
    const oldKey = await redis.HGET('schoolInviteKey', school)
    if (oldKey) await redis.HDEL('schoolUserInvite', oldKey)
    const key = Math.round(Date.now() / 1000).toString(36) + Acan.random(1000, 9999).toString(36)
    const data = await this.app.service('school-plan').getInfo({school}, params)
    await redis.HSET('schoolInviteKey', school, key)
    await redis.HSET('schoolUserInvite', key, JSON.stringify(data))
    return key
  }
  async getApply({school, name, avatar}: {school: string; name: string[]; avatar: string}, params: Params): Promise<any> {
    const {_id, email} = params.user ?? {}
    // 学生身份不能申请
    if (params.user?.role.includes('student')) return Promise.reject(new GeneralError({param: 'role', message: 'User Role error'}))
    const old = await this.Model.findOne({school, uid: _id})
    if (old) {
      // await this.Model.updateOne({_id: old._id}, {status: 2})
      return old
    }
    return await this.create({school, uid: _id, name, avatar, email, status: 1}, params)
  }
  async getJoin({_id}: {_id: string}, params: Params): Promise<any> {
    const {email}: any = await this.Model.findById(_id)
    if (email !== params.user?.email) return await Promise.reject(new PlanLimit('Your email address does not match the activation account', 'EmailError'))
    return await this.Model.updateOne({_id, status: 0}, {uid: params.user?._id, status: 2})
  }
  async getCheckEmail({email, school}: {email: string; school: string}, params?: Params): Promise<any> {
    return await this.Model.count({school, email})
  }
  // 批量检查 老师邮箱验证
  async getCheckEmails(query: any, params: Params) {
    // 检查是否是已经注册为学生
    let arr = await this.app.service('users').Model.aggregate([
      {
        $match: {email: query.email, roles: 'student'},
      },
      {
        $group: {_id: '$email', count: {$sum: 1}},
      },
    ])
    const student: any = {}
    for (const o of arr) {
      student[o._id] = o.count
    }
    // 检查学校下是否已经存在
    arr = await this.Model.aggregate([
      {$match: query},
      {
        $group: {_id: '$email', count: {$sum: 1}},
      },
    ])
    const teacher: any = {}
    for (const o of arr) {
      teacher[o._id] = o.count
    }
    return {teacher, student}
  }
  // 检查当前账号是否存在学校列表中
  async getCheckExists({school}: {school: string}, params: Params): Promise<any> {
    return await this.Model.findOne({school, uid: params.user?._id})
  }
  async getByEmail({email, school}: {email: string[]; school: string}, params?: Params): Promise<any> {
    return await this.Model.find({school, email: {$in: email}}).select(['email'])
  }
  async getSubjectStat({school}: {school: string}, params: Params): Promise<any> {
    let rs = await this.app.service('school-user').Model.find({school}).select('subject')
    const list: any = {}
    rs.map((v: any) => {
      for (const code of v.subject) {
        if (!list[code]) list[code] = {teacher: 0, class: 0}
        list[code].teacher++
      }
    })
    // todo subject stat count
    // rs = await this.getOldClassList({school: [school]}, params)
    // rs.map((v: any) => {
    //   if (!list[v.subject]) list[v.subject] = {teacher: 0, class: 0}
    //   list[v.subject].class++
    // })
    return list
  }
  async getClassList({school}: any, params: Params): Promise<any> {
    const doc: any = await this.Model.findOne({uid: params.user?._id, school, del: false, status: 2}).select(['class'])
    if (!doc || Acan.isEmpty(doc.class)) return []
    let classArr = doc.class.filter((v: string) => v != 'cloudRoom')
    return await this.app.service('classes').Model.find({_id: {$in: classArr}, del: false})
  }
  async getSchoolList(query: any, params: Params): Promise<any> {
    if (!params.user?._id) return []
    const select = ['name', 'avatar', 'nickname', 'school', 'role', 'uid']
    const rs: any = Acan.clone(await this.Model.find({uid: params.user._id, del: false, status: 2}).select(select))
    const arr = []
    for (const one of rs) {
      const schoolInfo = await this.app.service('school-plan').getInfo({school: one.school}, params)
      if (!schoolInfo) continue // old school filter
      Object.assign(one, {school: schoolInfo._id, schoolInfo})
      arr.push(one)
    }
    return arr
  }
  async getResend(arr: any, params: Params): Promise<any> {
    const id = arr[0].school
    const school = await this.app.service('school-plan').getInfo({school: id}, params)
    logger.warn('getResend:', id, arr, school)
    const rs = []
    for (const o of arr) {
      rs.push(await this.send(o, 'SchoolTeacherCreate', params, school))
    }
    return rs
  }
  async send(doc: any, tpl: string, params: Params, school?: any): Promise<any> {
    const {_id, school: id, name, email} = doc
    if (!school) school = await this.app.service('school-plan').getInfo({school: id})
    let url = ''
    if (tpl === 'SchoolTeacherCreate') url = `${SiteUrl}/v2/account/schoolJoin/${_id}`
    else if (tpl === 'SchoolTeacherAdminInvite') url = `${SiteUrl}/v2/account/schoolJoin/${_id}`
    else if (tpl === 'SchoolTeacherAdminSet') url = ''
    else if (tpl === 'SchoolTeacherAdminRemove') url = ''
    else if (tpl === 'SchoolTeacherApproved') url = ''
    else if (tpl === 'teacherremovedfromschool') url = ''
    return await this.app.service('notice-tpl').mailto(tpl, email, {nickname: name.join(' '), schoolName: school.name, url}, params.user?._id)
  }
  async createExt(one: any, params?: Params): Promise<any> {}

  async getCloudRoomCount({school}: any, params: Params): Promise<any> {
    let count = await this.Model.count({school, class: 'cloudRoom'})
    return {
      count,
    }
  }
}
