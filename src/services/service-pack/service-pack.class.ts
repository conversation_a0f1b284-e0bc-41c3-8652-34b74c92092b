import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import hook from '../../hook'
import logger from '../../logger'

export class ServicePack extends Service {
  app: Application
  selectList: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.selectList = [
      'name',
      'cover',
      'coverName',
      'type',
      'mentoringType',
      'countryCode',
      'curriculum',
      'subject',
      'gradeGroup',
      'price',
      'contentOrientatedConfig',
      'consultant',
      'discount',
      'discountConfig',
      'freq',
      'duration',
      'break',
      'status',
      'count',
      'createdAt',
    ]
  }
  incCount(_id: string, $inc: any, params?: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    return this.Model.updateOne({_id}, {$inc}, options)
  }
  // 检查折扣配置是否过期
  checkDiscount(one: any) {
    if (!one.discountConfig) return // 不存在配置
    const {enable, end} = one.discountConfig
    if (enable && !end) return // 折扣长期有效
    if (enable && new Date(end).getTime() > Date.now()) return // 正常
    // 折扣过期，需要关闭
    one.discountConfig.enable = false
    this.Model.updateOne({_id: one._id}, {'discountConfig.enable': false})
    return one
  }

  // 服务包列表
  async getIndex(con: any, params: Params) {
    Object.assign(con, {
      status: true,
      contentOrientatedEnable: true, // 只取主题服务包
      $sort: {lastPublished: -1}, // 按最后发布时间最新的排序
    })
    if (!con.salesTarget) con.salesTarget = 'personal' // 默认获取个人的数据
    if (hook.roleHas(['student'])({params})) {
      con.mentoringType = {$ne: 'teacherTraining'}
    } else if (con.salesTarget === 'school') {
      // 老师在学校身份下取所有的数据
    } else {
      con.mentoringType = 'teacherTraining'
    }
    // 排除自己的数据
    return this.indexList(con, params)
  }
  async indexFind(con: any, params: Params) {
    if (con.sessionType === 'student' || con.type === 'selfStudy') delete con.regDate
    if (con.key) {
      con.name = {$regex: con.key.trim(), $options: 'i'}
      delete con.key
    }
    const data = Acan.clone(
      await this.Model.find(con)
        .sort(con.$sort ? con.$sort : {updatedAt: -1})
        .select(this.selectList)
        .skip(con.$skip || 0)
        .limit(con.$limit || 10)
    )
    for (const one of data) {
      this.checkDiscount(one)
    }
    return data
  }
  async indexList(con: any, params: Params) {
    const data = await this.indexFind(con, params)
    const rs: any = {total: await this.Model.count(con), limit: parseInt(con.$limit || 10), skip: parseInt(con.$skip || 0), data}
    if (isDev) rs.query = con
    return rs
  }
  // 服务包移除主题课件
  async upByAuth({unit}: any) {
    const arr: any = Acan.clone(await this.Model.find({'contentOrientated.premium': unit._id}).select(['contentOrientated']))
    if (Acan.isEmpty(arr)) return
    for (const o of arr) {
      const co = o.contentOrientated.find((v: any) => v.premium === unit._id)
      const post: any = {
        $pull: {contentOrientated: {_id: co._id}}, // 解除主题课件
      }
      // 没有主题课，下架并设置为未完善
      if (o.contentOrientated.length === 1) {
        Object.assign(post, {status: false, reason: 'service-auth remove', filled: false})
      }
      await this.Model.updateOne({_id: o._id}, post)
    }
    return arr
  }
  // 统计
  async groups(query: any, key: string) {
    const arr = await this.Model.aggregate([
      {$match: query},
      {
        $group: {
          _id: '$' + key,
          count: {$sum: 1},
        },
      },
    ])
    const rs: any = {}
    for (const {_id, count} of arr) {
      rs[_id] = count
    }
    return rs
  }
  // 统计认证老师数量，用于发布服务包看
  async getGroups(query: any) {
    const subject: any = await this.groups(query, 'subject')
    // 多学科数组数据处理
    for (const key in subject) {
      if (!key) delete subject[key]
      if (key.includes(',')) {
        for (const k of key.split(',')) {
          if (!subject[k]) subject[k] = subject[key]
          else subject[k] += subject[key]
        }
        delete subject[key]
      }
    }
    const rs = {
      mentoringType: await this.groups(query, 'mentoringType'),
      curriculum: await this.groups(query, 'curriculum'),
      subject,
    }
    return rs
  }

  async extSchoolPrice(one: any, school: any, params?: Params) {
    one.schoolPrice = await this.app.service('service-pack-school-price').Model.findOne({servicePack: one._id, school})
  }
  // 解绑关联服务包并自动下架 https://github.com/zran-nz/bug/issues/5359
  async unBindPack(one: any) {
    const rid = one._id
    let arr
    // unbind 1v1mentor
    arr = Acan.clone(await this.Model.find({status: true, 'contentOrientated.servicePack': rid}).select(['status', 'contentOrientated']))
    if (arr)
      for (const o of arr) {
        const contentOrientated = o.contentOrientated.filter((v: any) => {
          return v.servicePack !== rid
        })
        await this.Model.updateOne({_id: o._id}, {$set: {status: false, contentOrientated}})
      }
    // unbind consultant
    arr = Acan.clone(await this.Model.find({status: true, 'consultant.servicePack': rid}).select(['status', 'consultant']))
    if (arr)
      for (const o of arr) {
        await this.Model.updateOne({_id: o._id}, {$set: {status: false}, $unset: {consultant: ''}})
      }
    // unbind carerPack
    arr = Acan.clone(await this.Model.find({status: true, 'carerPack._id': rid}).select(['status', 'carerPack']))
    if (arr)
      for (const o of arr) {
        await this.Model.updateOne({_id: o._id}, {$set: {status: false}, $unset: {carerPack: ''}})
      }
    // unbind interviewPack
    arr = Acan.clone(await this.Model.find({status: true, 'interviewPack._id': rid}).select(['status', 'interviewPack']))
    if (arr)
      for (const o of arr) {
        await this.Model.updateOne({_id: o._id}, {$set: {status: false}, $unset: {interviewPack: ''}})
      }
  }

  /*
  teacher下的students view 侧边栏统计 https://github.com/zran-nz/bug/issues/5315
  找出老师可以服务的服务包，进行分类
  1v1 Mentor - 对应后台的Mentor，Content orientated选择No
  Interview service - 对应后台的Education consultant，Consultant type选择Interview for student enrolment
  Carer Service - 对应后台的Education consultant，Consultant type选择Carer service package
*/
  async getTeacherSideViewData({}: any, params: Params) {
    const packSelect = ['type', 'mentoringType', 'countryCode', 'curriculum', 'subject', 'topic', 'gradeGroup', 'qualification', 'serviceRoles']
    // 找出老师认证项
    const queryAuth = {
      uid: params.user?._id,
      status: 2,
    }
    const auths = await this.app.service('service-auth').Model.find(queryAuth).select(packSelect).limit(1000)
    // 找出服务包
    const query = {
      $or: [
        {
          serviceRoles: 'mentoring',
          contentOrientatedEnable: false,
        },
        {
          serviceRoles: 'consultant',
          'consultant.type': {$in: ['carer', 'interview']},
        },
      ],
    }
    const list: any = Acan.clone(
      await this.Model.find(query)
        .select(['name', 'cover', 'freq', 'duration', 'break', 'consultant', ...packSelect])
        .limit(1000)
    )
    // 匹配是否符合用户的服务包
    const data = list.filter((pack: any) => {
      const arr = auths.filter((v: any) => {
        for (const key of packSelect) {
          if (Acan.isEmpty(pack[key])) continue //跳过空的项
          if (Array.isArray(pack[key])) {
            if (Array.isArray(v[key])) {
              // 数组判断交集存在
              if (Acan.isEmpty(Acan.intersection(pack[key], key === 'topic' ? v[key].map((v: any) => v._id) : v[key]))) {
                // console.log(key, pack[key], v[key], 'not match!')
                return false
              }
            } else if (!pack[key].includes(v[key])) {
              return false
            }
          } else {
            if (Array.isArray(v[key])) {
              if (!v[key].includes(pack[key])) {
                // console.log(key, pack[key], v[key], 'not match!')
                return false
              }
            } else if (pack[key] !== v[key]) {
              // console.log(key, pack[key], v[key], 'not match!')
              return false
            }
          }
        }
        if (isDev) logger.log(v, pack, 'match')
        return true // 条件完全匹配
      })
      pack.auths = arr.map((v) => v._id)
      return !Acan.isEmpty(arr)
    })
    if (isDev) return {data, query, queryAuth, auths}
    else return {data, auths}
  }
}
