import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {ObjectID} from 'bson'
import logger from '../../logger'
const roleMap: any = {read: 'reader', write: 'writer', comment: 'commenter', owner: 'owner'}

export class Collab extends Service {
  app: Application
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async isMember({rid}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    return await this.Model.findOne({rid, 'members.email': params.user?.email}, null, options)
  }
  // return {[unit._id]: {member}}
  async getByRid($in: any, params: Params) {
    if (Acan.isObj($in)) $in = Object.values($in)
    if (Acan.isEmpty($in)) return {}
    const email = params.user?.email
    const rs: any = Acan.clone(await this.Model.find({rid: {$in}}).select(['rid', 'members']))
    const list: any = {}
    rs.map((one: any) => {
      list[one.rid] = Object.assign({collab: one._id, ...one.members.find((v: any) => v.email === email)})
    })
    return list
  }
  async batchInvite(_id: string, data: any, params: Params): Promise<any> {
    const {email, role, message} = data
    if (Acan.isEmpty(email)) return []
    let fileId
    const doc = await this.get(_id)
    if (!doc) return {message: 'not find data'}
    if (['task', 'pdTask', 'video'].includes(doc.type)) {
      fileId = await this.getFileId(doc, params)
      if (!fileId) return {message: (doc.type === 'video' ? 'video' : 'slides') + ' not created'}
    }
    const oldMembers: any = {}
    doc.members.map((v: any) => {
      oldMembers[v.email] = v._id
    })
    const members = []
    for (const v of email) {
      if (!v || oldMembers[v]) continue // has exist
      const member: any = {_id: new ObjectID().toString(), email: v, nickname: v, role, status: true}
      const user = await this.app.service('users').byMail(v)
      if (user?.avatar) member.avatar = user.avatar
      if (user?.nickname) member.nickname = user.nickname
      members.push(member)
    }
    if (Acan.isEmpty(members)) return []
    await this.batchSend({code: 'CollabInvite', members, message}, doc, params)

    if (fileId) {
      const post = {fileId, message, role}
      for (const one of members) {
        const rs = await this.shareWithEmail({...post, email: one.email}, params)
        logger.warn(rs, one.email)
        if (rs?.id) one.permissionId = rs.id
      }
    }
    await this.Model.updateOne({_id}, {$addToSet: {members}})
    // logger.warn(_id, members, doc.type)
    return members
  }
  async getFileId(doc: any, params: Params): Promise<any> {
    if (!['task', 'pdTask', 'video'].includes(doc.type)) return null
    if (doc.type === 'video') {
      const {video}: any = await this.app.service('unit').Model.findById(doc.rid).select('video')
      return video
    } else {
      const {sid}: any = await this.app.service('unit').Model.findById(doc.rid).select('sid')
      return sid
    }
  }
  editUrl({type, rid, pop}: any, params: Params) {
    const editType = type.includes('video') ? 'video' : type.includes('unit') ? 'unit' : 'task'
    const ubj = new URL(`${SiteUrl}/v2/com/${editType}/edit/${rid}`)
    ubj.searchParams.set('back', '/home/<USER>')
    if (pop) ubj.searchParams.set('pop', pop)
    return ubj.href
  }
  async collaberToAuthor(doc: any, {collaberName, message, code}: any, params: Params) {
    const {nickname: author, email} = await this.app.service('users').uidToInfo(doc.uid)
    const {name: unitName}: any = await this.app.service('unit').Model.findById(doc.rid).select('name')
    const url = this.editUrl({...doc, pop: 'collab'}, params)
    this.app.service('notice-tpl').mailto(code, email, {url, unitName, author, collaberName, message}, params.user?._id)
  }
  // author to collaber
  async batchSend(data: any, doc: any, params: Params): Promise<any> {
    const {members, message, code} = data
    const {rid, type} = doc
    const {name: unitName}: any = await this.app.service('unit').Model.findById(rid).select('name')
    if (!unitName) return {}
    const {_id, nickname} = params.user ?? {}
    for (const member of members) {
      const url = this.editUrl(doc, params)
      const post: any = {author: nickname, collaberName: member.nickname, url, unitName, message}
      if (code === 'CollabReviewReject') delete post.url
      await this.app.service('notice-tpl').mailto(code, member.email, post, _id)
    }
    return {}
  }
  async getMemberInfo({_id}: any, params: Params): Promise<any> {
    return await this.Model.findOne({'members._id': _id}).select(['rid', 'type', 'uid', 'members.$'])
  }
  async getSharedMembers({}, params: Params): Promise<any> {
    const list = await this.Model.distinct('members.email', {uid: params.user?._id})
    const rs = []
    for (const email of list) {
      rs.push(await this.app.service('users').byMail(email))
    }
    return rs
  }
  async getApply({_id, message}: any, params: Params): Promise<any> {
    const {email, nickname, avatar} = params.user ?? {}
    const doc: any = Acan.clone(await this.Model.findById(_id))
    const member = doc.members.find((m: any) => m.email === email)
    if (member) return {message: 'Already apply'}
    const members: any = {_id: new ObjectID().toString(), email, avatar, nickname, status: false, message}
    const rs = await this.Model.updateOne({_id}, {$addToSet: {members}})
    this.collaberToAuthor(doc, {code: 'CollabApply', collaberName: nickname, message}, params)
    return rs
  }
  async getReview({_id, role, status}: any, params: Params): Promise<any> {
    const $set: any = {'members.$.status': status}
    if (role) $set['members.$.role'] = role
    const doc = await this.getMemberInfo({_id}, params)
    if (status) {
      if (doc?.members[0]) {
        const {email} = doc.members[0]
        const fileId = await this.getFileId(doc, params)
        if (fileId) {
          const rs = await this.shareWithEmail({fileId, email, role, noticeEmail: true}, params)
          if (rs?.id) $set['members.$.permissionId'] = rs.id
        }
      }
      this.batchSend({code: 'CollabReviewAccept', members: doc.members}, doc, params)
    }
    return await this.Model.updateOne({'members._id': _id}, {$set})
  }

  // async shareWithAnyOne(doc: any, params: Params): Promise<any> {
  //   const fileId = await this.getFileId(doc, params)
  //   const drive = await this.app.service('slides').driveInit(params.user?.google)
  //   return await drive.permissions.create({
  //     fileId,
  //     requestBody: {
  //       role: 'reader',
  //       type: 'anyone',
  //     },
  //   })
  // }

  async autoJoin(doc: any, params: Params): Promise<any> {
    const {email, nickname, avatar} = params.user ?? {}
    const fileId = await this.getFileId(doc, params)
    const members: any = {_id: new ObjectID().toString(), email, avatar, nickname, role: doc.guest, status: true}
    if (fileId) {
      const google = await this.app.service('users').getGoogle({_id: doc.uid})
      const rs = await this.shareWithEmail({fileId, email, role: doc.guest, noticeEmail: false, google}, params)
      if (rs?.id) members.permissionId = rs.id
    }
    await this.Model.updateOne({_id: doc._id}, {$addToSet: {members}})
    doc = Acan.clone(doc)
    doc.members.push(members)
    this.collaberToAuthor(doc, {code: 'CollabAutoJoin', collaberName: members.nickname}, params)
    logger.warn(members, 'autoJoin')
    return doc
  }

  async shareWithEmail(data: any, params: Params): Promise<any> {
    const {fileId, email, message, role, noticeEmail, google} = data
    const drive = await this.app.service('slides').driveInit(google || params.user?.google)
    const post: any = {
      fileId,
      sendNotificationEmail: noticeEmail ?? true,
      supportsAllDrives: true,
      requestBody: {
        role: roleMap[role],
        type: 'user',
        emailAddress: email,
      },
    }
    if (message) post.emailMessage = message
    logger.warn('shareWithEmail:', post)
    const rs = await drive.permissions.create(post).catch((e: any) => {
      logger.warn(e, e.message, post)
      return {}
    })
    logger.warn('shareWithEmail:', rs)
    return rs?.data
  }
  async shareUpRole(doc: any, params: Params): Promise<any> {
    if (!['task', 'pdTask', 'video'].includes(doc.type)) return
    const fileId = await this.getFileId(doc, params)
    if (!fileId) return logger.warn('no fileId')
    const member = Acan.clone(doc.members).find((v: any) => v._id === params.query?.['members._id'])
    if (!member || !member.permissionId) return logger.warn(member, params.query, 'shareUpRole error')
    const post = {fileId, permissionId: member.permissionId, requestBody: {role: roleMap[member.role]}}
    logger.warn(post, 'upRole')
    const drive = await this.app.service('slides').driveInit(params.user?.google)
    return await drive.permissions.update(post)
  }
  async shareRemove(memberId: String, params: Params): Promise<any> {
    const doc: any = await this.Model.findOne({'members._id': memberId}).select(['id', 'rid', 'type', 'members.$'])
    if (!doc || !doc.members[0].permissionId) {
      this.batchSend({code: 'CollabReviewReject', members: doc.members}, doc, params)
      return
    }
    const fileId = await this.getFileId(doc, params)
    if (!fileId) return logger.warn('no fileId')
    const post = {fileId, permissionId: doc.members[0].permissionId}
    const drive = await this.app.service('slides').driveInit(params.user?.google)
    // 移除调用一次就行，失败也要正常返回
    const rs = await drive.permissions.delete(post).catch((e: any) => {
      logger.warn(e, e.message, post)
      return {message: e.message}
    })
    logger.warn(post, 'shareRemove', rs?.data, rs?.status)
    return rs
  }

  // async getLinkKey({_id}: {_id: string}, params: Params): Promise<any> {
  //   const key = await this.app.get('redis').HGET('collabIdKey', _id)
  //   if (key) return key
  //   return await this.getResetLink({_id}, params)
  // }
  // async getLinkInfo({key}: {key: string}, params: Params): Promise<any> {
  //   const _id = await this.app.get('redis').HGET('collabKeyId', key)
  //   return await this.app.service('collab').get(_id)
  // }
  // async getResetLink({_id}: {_id: string}, params: Params): Promise<any> {
  //   const redis = this.app.get('redis')
  //   const oldKey = await redis.HGET('collabIdKey', _id)
  //   if (oldKey) await redis.HDEL('collabKeyId', oldKey)
  //   const key = Math.round(Date.now() / 1000).toString(36) + Acan.random(1000, 9999).toString(36)
  //   await redis.HSET('collabIdKey', _id, key)
  //   await redis.HSET('collabKeyId', key, _id)
  //   return key
  // }
}
