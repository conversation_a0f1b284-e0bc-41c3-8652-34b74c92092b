import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'
import {BadRequest} from '@feathersjs/errors'

export class ServiceBooking extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async extUser(result: any, params: Params) {
    if (result.booker) result.bookerInfo = await this.app.service('users').uidToInfo(result.booker)
    if (result.servicer) result.servicerInfo = await this.app.service('users').uidToInfo(result.servicer)
    if (result.packUser) result.servicePackUser = await this.app.service('service-pack-user').toInfo(result.packUser)
    return result
  }
  async extSession(result: any, params: Params) {
    if (result.oldSession?._id && params.extQuery?.oldSession)
      result.oldSessionInfo = await this.app
        .service('session')
        .Model.findOne({_id: result.oldSession._id}, {name: 1, image: 1, start: 1, end: 1, questions: 1, materials: 1, 'task.pages': 1, pages: '$task.pages'})
    if (result.session) result.sessionInfo = await this.app.service('session').toInfo(result.session._id)
    return result
  }
  // 服务者的时间段
  async getHours({uid, end}: any) {
    const query: any = {servicer: uid, cancel: null, start: {$gt: new Date()}}
    if (end) query.start.$lt = end
    const arr = await this.Model.find(query).sort({start: 1}).limit(1000).select(['servicer', 'start', 'end'])
    return arr.map((v: any) => {
      if (uid.$in) return {uid: v.servicer, hour: [v.start, v.end]} // 批量查询多个老师
      return [v.start, v.end]
    })
  }
  // 预订者的已用时间段
  async getMyHours({}: any, params: Params) {
    const uid = params.user?._id
    const arr = await this.Model.find({booker: uid, cancel: null, start: {$gt: new Date()}})
      .sort({start: 1})
      .limit(1000)
      .select(['start', 'end'])
    const booking = arr.map((v: any) => {
      return [v.start, v.end]
    })
    const session = await this.app.service('session').getHours({uid})
    return {booking, session}
  }
  // 检查该时段是否可以创建预订
  async checkHours({start, end}: any, params: Params) {
    const uid = params.user?._id
    const $or = [{start: {$gt: start, $lt: end}}, {end: {$gt: start, $lt: end}}]
    let query: any = {$or, booker: uid, cancel: null}
    const booking = await this.Model.count(query)
    if (booking) return Promise.reject(new Error('has booking'))
    const arr = []
    for (const o of [{'reg._id': uid, school: null}, {uid}]) {
      for (const or of $or) arr.push({...or, ...o})
    }
    query = {$or: arr, sessionType: 'live', booking: null, status: {$ne: 'close'}}
    const session = await this.app.service('session').Model.count(query)
    if (session) return Promise.reject(new BadRequest('has session', query))
  }

  async cron1({}: any, params?: Params): Promise<any> {
    try {
      const rs = await this.getSessionTimeout()
      this.unscheduledRemind()
      this.classStartsRemind()
      this.autoImportLecture()
      return rs
    } catch (error: any) {
      this.app.service('log').create({type: 'node.booking.cron', ip: global.LocalIp, body: error.message, stack: error.stack, msg: 'bookingCron error'}).then()
    }
  }
  // 未排课提醒,开课12小时内
  unscheduledRemind() {
    this.Model.find({
      session: null,
      cancel: null,
      reminder: {$lt: 1},
      start: {$lt: Date.now() + 12 * 60 * 60 * 1000, $gt: Date.now() + 1 * 60 * 60 * 1000},
    }).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const booking = rs[i]
        const user = await this.app.service('users').uidToInfo(booking.servicer)
        this.app
          .service('notice-tpl')
          .send(
            'Remindteacher12hoursbeforementoring',
            {_id: user._id, email: user.email},
            {username: user.nickname, start_time: new Date(booking.start).toLocaleString(), url: `${SiteUrl}/v2/detail/booked/${booking._id}`}
          )

        await this.Model.updateOne({_id: booking._id}, {$inc: {reminder: 1}})
      }
    })
  }
  // 预约后,开课前12小时提醒
  async classStartsRemind() {
    this.Model.find({
      cancel: null,
      reminderBooked: {$lt: 1},
      start: {$lt: Date.now() + 12 * 60 * 60 * 1000, $gt: Date.now() + 1 * 60 * 60 * 1000},
    })
      .populate({path: 'packUser', model: this.app.service('service-pack-user').Model})
      .then(async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const booking = rs[i]
          if (booking.packUser.gift && booking.packUser.price == 0 && booking.packUser.total == 1) {
            const user = await this.app.service('users').uidToInfo(booking.servicer)
            this.app.service('notice-tpl').send('RemindTeacher12hoursWhenBooked', {_id: user._id, email: user.email}, {username: user.nickname})
            await this.Model.updateOne({_id: booking._id}, {$inc: {reminderBooked: 1}})
          }
        }
      })
  }
  // 补买/复购后 自动取消 之前的预约
  async lectureCancelByOrder({packUserDoc}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const list = Acan.clone(await this.Model.find({packUser: packUserDoc._id, start: {$gt: new Date()}, cancel: null}, null, options))
    for (const o of list) {
      await this.patchCancel({_id: o._id}, params)
    }
    return
  }
  // 老师排课超时，自动取消预约
  async getSessionTimeout() {
    const arr: any = Acan.clone(await this.Model.find({start: {$lt: new Date()}, session: null, cancel: null}).limit(1000))
    for (const one of arr) {
      // 服务次数加回来
      const {packUser, packUserData, packUserTasks, times} = one
      if (!Acan.isEmpty(one.packUserData))
        await this.app.service('service-pack-user-data').add({packUserData, packUserTasks, packUser, times, type: 'timeout'}, {})
      await this.Model.updateOne({_id: one._id}, {$set: {cancel: 'timeout'}})
      this.lag({servicer: one.servicer})

      // To teacher
      const teacher = await this.app.service('users').uidToInfo(one.servicer)
      this.app.service('notice-tpl').send(
        'bookedmentorsessionfailedtoscheduleTeacher',
        {_id: teacher._id, email: teacher.email},
        {
          username: teacher.nickname,
          start_time: new Date(one.start).toLocaleString(),
        }
      )
      // To student
      const student = await this.app.service('users').uidToInfo(one.booker)
      this.app.service('notice-tpl').send(
        'bookedmentorsessionfailedtoscheduleStudent',
        {_id: student._id, email: student.email},
        {
          username: student.nickname,
          start_time: new Date(one.start).toLocaleString(),
        }
      )
    }
    return arr
  }
  // 计算滞后显示是否大于1/3 service-conf.lag
  async lag({servicer}: any, params?: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const rs = await this.Model.aggregate([{$match: {servicer}}, {$group: {_id: '$cancel', count: {$sum: 1}}}], options)
    let lag = 0
    let total = 0
    for (const o of rs) {
      total += o.count
      if (['servicer', 'timeout'].includes(o._id)) lag += o.count
    }
    if (lag / total > 1 / 3) {
      // 取terminate/cancel的超过1/3的被预约辅导课的老师，条件为：1周内不显示在所有预约列表中，1周后恢复正常
      // https://github.com/zran-nz/bug/issues/4455#issuecomment-2454089544
      // const days = 7
      // const startAt = new Date()
      // const endAt = new Date(startAt.getTime() + 86400 * days * 1000)
      // await this.app.service('suspend-class').create({uid: servicer, lag: true, days, startAt, endAt})
      // 又不拉黑了 https://github.com/zran-nz/bug/issues/6132
      // await this.app.service('suspend-class').suspend({type: 'frequent-cancellation', days, uid: servicer, lag: true})
      // await this.app.service('service-conf').Model.updateOne({_id: servicer}, {$set: {lag: true}})
    } else {
      // await this.app.service('service-conf').Model.updateOne({_id: servicer}, {$set: {lag: false}})
    }
  }
  // 取消预约，
  // 删除关联 session
  // 恢复服务包次数 service-pack-user.used -= service-booking.times
  // 计算滞后显示是否大于1/3 service-conf.lag
  async patchCancel(data: any, params: Params) {
    const dbSession = params?.mongoose?.session
    const options = dbSession ? {session: dbSession} : {}
    const {_id} = data
    const doc: any = await this.Model.findById(_id, null, options)
    if (doc.cancel) return Promise.reject(new Error('has cancel'))
    const {packUser, packUserTasks, times, session, servicer, oldSession, start, booker, serviceAuthId} = doc
    const uid = params.user?._id
    // 识别取消的身份
    if (booker === uid) data.cancel = 'booker'
    else if (servicer === uid) data.cancel = 'servicer'
    else return Promise.reject(new Error('error cancel user'))

    if (session?._id)
      await this.app.service('session').remove(session._id, {
        mongoose: {session: params?.mongoose?.session},
      })
    // 学员在2小时内取消不加回次数 https://github.com/zran-nz/bug/issues/5492
    if (data.cancel === 'servicer' || new Date(doc.start).getTime() - Date.now() > 7200000)
      await this.app
        .service('service-pack-user-data')
        .add({packUserData: doc.packUserData, packUserTasks, packUser, times, type: 'cancel', servicer, oldSession, start}, params || {})

    const packUserData: any = await this.app.service('service-pack-user').Model.findById(packUser, null, options)
    // if (data.cancel == 'servicer') {
    //   const user = await this.app.service('users').uidToInfo(booker)
    //   this.app.service('notice-tpl').send(
    //     'mentorservicecanceledbyprovider',
    //     {_id: user._id, email: user.email},
    //     {
    //       username: user.nickname,
    //       service_name: packUserData.snapshot.name,
    //     }
    //   )
    // }

    let startTime = new Date(doc.start)
    let endTime = new Date(doc.end)
    let option: any = {timeZoneName: 'short', hour12: false}

    if (data.cancel == 'booker') {
      const user = await this.app.service('users').uidToInfo(servicer, params)
      let name = session?.name
      if (!name) {
        name = packUserData.snapshot.name
      }
      option.timeZone = user.timeZone || 'Asia/Shanghai'
      let time = `${startTime.toLocaleString('en', option)} - ${endTime.toLocaleString('en', option)}`

      this.app.service('notice-tpl').send(
        'MentoringServiceCanceled',
        {_id: user._id, email: user.email},
        {
          name: name,
          time,
        }
      )
    }

    const bookerData = await this.app.service('users').uidToInfo(booker, params)
    option.timeZone = bookerData.timeZone || 'Asia/Shanghai'
    let time = `${startTime.toLocaleString('en', option)} - ${endTime.toLocaleString('en', option)}`
    this.app.service('notice-tpl').send(
      'CancellationOfTheSessionBooking',
      {_id: bookerData._id, email: bookerData.email},
      {
        name: packUserData.snapshot.name || '',
        username: bookerData.name.join(' '),
        url: `${SiteUrl}/v2/detail/booking/my/${packUser}`,
        time,
      }
    )

    if (doc.servicePackApply) {
      await this.app.service('service-pack-apply').Model.updateOne({_id: doc.servicePackApply}, {interviewApply: false}, options)
    }
    // await this.pushImportUsers({serviceAuthId: doc.serviceAuthId, uid: doc.servicer})
    if (serviceAuthId) {
      // 重置面试状态
      await this.app.service('service-auth').Model.updateOne({_id: serviceAuthId}, {interviewApply: false}, options)
    }
    const rs = await this.patch(_id, {cancel: data.cancel, canceledAt: new Date()}, {mongoose: {session: params?.mongoose?.session}})
    if (data.cancel == 'servicer') this.lag({servicer: doc.servicer}, params)
    return rs
  }
  // 删除session后解绑
  async unBindSession(booking: any, params: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    const doc: any = await this.patch(booking, {$unset: {session: ''}}, {mongoose: {session: params?.mongoose?.session}})
    // await this.pushImportUsers({serviceAuthId: doc.serviceAuthId, uid: doc.servicer})
  }
  async getImportByBooking({serviceAuthId, bookingId, order}: any, params: Params) {
    return await this.importByBooking({serviceAuthId, bookingId, order}, params)
  }
  // 认证精品课快照购买支付成功后 自动排课
  async importByBooking({serviceAuthId, bookingId, order}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    let unitSnapshot: any
    let linkSnapshot: any
    if (order) {
      const orderDoc: any = await this.app.service('order').Model.findById(order, null, options)
      unitSnapshot = orderDoc.links[0].goods.unitSnapshot
      linkSnapshot = orderDoc.links[0].goods.linkSnapshot
    } else {
      const authDoc: any = await this.app.service('service-auth').Model.findById(serviceAuthId, null, options)
      unitSnapshot = authDoc.unitSnapshot
      linkSnapshot = authDoc.linkSnapshot
    }
    const bookingDoc: any = Acan.clone(await this.Model.findById(bookingId, null, options))
    await this.Model.updateOne({_id: bookingId}, {$set: {serviceAuthId}}, options)
    const {questions, materials}: any = unitSnapshot
    for (const key of ['questions', 'materials']) {
      delete unitSnapshot[key]
    }
    const sessionDoc = await this.app.service('session').create(
      {
        type: 'bookingStu' + unitSnapshot.mode.toFirstUpperCase(),
        sessionType: 'live',
        name: unitSnapshot.name,
        image: unitSnapshot.cover,
        unitType: unitSnapshot.type,
        subjects: unitSnapshot.subjects,
        start: bookingDoc.start,
        end: bookingDoc.end,
        zoom: {
          capacity: 0,
          checked: false,
          passcode: false,
          invalid: false,
          waiting_room: false,
        },
        students: [bookingDoc.booker],
        uid: bookingDoc.servicer,
        questions,
        materials,
        booking: bookingId,
        task: unitSnapshot,
        order,
      },
      {...params}
    )
    const {_id, name, image, status} = sessionDoc
    await this.Model.updateOne({_id: bookingId}, {$set: {session: {_id, name, image, status}}}, options)
    await this.autoCreateLinkSession({pid: sessionDoc._id.toString(), booing: bookingDoc, linkSnapshot, unitSnapshot}, params)
    return sessionDoc
  }
  // 认证精品课快照购买后自动排课 被取消添加已购标记
  // async pushImportUsers({serviceAuthId, uid}: any) {
  //   return await this.app.service('service-auth').Model.updateOne({_id: serviceAuthId}, {$push: {importUsers: uid}})
  // }
  async getAutoSession({_id}: any, params: Params) {
    const doc = Acan.clone(await this.Model.findById(_id))
    return (await this.autoCreateSession(doc, params)) || {}
  }
  // 面试服务包预约自动排课逻辑 https://github.com/zran-nz/bug/issues/5302
  async autoCreateSession(one: any, params: Params) {
    if (!one.servicePackApply) return Promise.reject(new BadRequest('no servicePackApply')) // 非面试申请的
    const packUser: any = await this.app.service('service-pack-user').Model.findById(one.packUser)
    const typeMap: any = {
      interview: Agl.templateTab[1],
      interviewTeacher: Agl.templateTab[3],
    }
    const sessionMap: any = {
      interview: 'bookingStuTask',
      interviewTeacher: 'jobSeekerTask',
    }
    const {serviceRoles, consultant, cover, name} = packUser.snapshot
    if (serviceRoles !== 'consultant') return // Promise.reject(new BadRequest('servicePack serviceRoles not match')) // 不符合
    if (!typeMap[consultant.type]) return // Promise.reject(new BadRequest('servicePack consultant.type not match')) // 不符合
    // 获取默认模板
    const template: any = await this.app.service('template').Model.findOne({default: true, tab: typeMap[consultant.type]})
    if (!template) return Promise.reject(new BadRequest('default template not config')) // 没有设置默认模板
    const {pages} = await this.app.service('slides').get(template.id)
    const questions = await this.app.service('questions').Model.find({id: template.id}).select(this.app.service('questions').questionSelect)
    const materials = await this.app.service('materials').Model.find({id: template.id})
    const post = {
      type: sessionMap[consultant.type],
      name,
      isAutoCreate: true,
      image: cover,
      // unitType: '', // unit.type
      sessionType: 'live', // unit.sessionType
      // zoom: { passcode: true/false, waiting_room: true/false },
      // category?: '', color?: '',
      // subjects?: [...],
      students: [one.booker], // session student user_id
      uid: one.servicer,
      pages, // slides.pages snapshot
      questions,
      materials,
      booking: one._id,
      start: one.start,
      end: one.end,
    }
    const doc: any = await this.app.service('session').create(post)
    const session = {
      _id: doc._id,
      name: doc.name,
      image: doc.image,
      status: doc.status,
    }
    await this.Model.updateOne({_id: one._id}, {$set: {session}})
    return post
  }
  // 获取booking中的lecture对应的task快照
  async getLectureTaskSnapshot({_id, packUser}: any, params: Params) {
    const {premium, tasks, taskIndex}: any = await this.app.service('service-pack-user').Model.findById(packUser).select(['premium', 'tasks', 'taskIndex'])
    if (Acan.isEmpty(tasks)) return Promise.reject(new BadRequest(`not found tasks in packUser: ${packUser}`))
    const arr = Acan.clone(await this.Model.find({packUser, 'session._id': null}).select(['_id']))
    const index = arr.findIndex((v: any) => v._id === _id) // 预订所在的索引
    const taskId = tasks[index]
    if (!taskId) return Promise.reject(new BadRequest(`no task for booking!`))
    const {linkSnapshot}: any = await this.app
      .service('service-auth')
      .Model.findById(premium)
      .select([`linkSnapshot.${taskId}`])
    logger.info(premium, taskId, taskIndex, Object.keys(linkSnapshot))
    if (!linkSnapshot) return Promise.reject(new BadRequest(`not found linkSnapshot: auth._id: ${premium}, taskId: ${taskId}`))
    return linkSnapshot[taskId] || null
  }
  async getTest({_id}: any) {
    return await this.autoImportLecture()
    // const one = Acan.clone(await this.Model.findById(_id))
    // return await this.importLecture(one)
  }
  // 开课前2小时，系统自动导入匹配的精品lecture快照 https://github.com/zran-nz/bug/issues/5561
  async autoImportLecture() {
    const query = {'session._id': null, cancel: null, start: {$gt: new Date(), $lte: new Date(Date.now() + 3600 * 2000)}}
    const list = Acan.clone(await this.Model.find(query).limit(100).select('start end booker servicer cancel packUser packUserData packUserTasks'))
    const arr = []
    for (const o of list) {
      const rs = await this.importLecture(o)
      this.app.service('log').create({type: 'node.booking.autoImportLecture', ip: global.LocalIp, body: rs, msg: 'autoImportLecture'}).then()
      arr.push(rs)
    }
    return {list, query, arr}
  }
  async importCreateSession(task: any, ext: any) {
    const {questions, pages, materials} = task
    const post = {
      type: 'bookingStu' + task.mode.toFirstUpperCase(),
      name: task.name,
      image: task.cover,
      unitType: task.type, // unit.type
      // unit.sessionType
      subjects: task.subjects,
      isAutoCreate: true,
      pages,
      task,
      questions,
      materials,
      ...ext,
      // booking: _id,
      // start,
      // end,
    }
    return await this.app.service('session').create(post)
  }
  async getTestImportLecture({bookingId}: any) {
    const doc = Acan.clone(await this.Model.findById(bookingId)) // .select('start end booker servicer cancel packUser packUserData packUserTasks')
    return await this.importLecture(doc)
  }
  // 系统自动导入匹配的精品lecture快照，课程进入到scheduled状态
  async importLecture({_id, packUser, start, end, booker, servicer}: any) {
    // const {packUser, start, end, booker}: any = await this.Model.findById(_id).select(['start', 'end', 'packUser', 'booker'])
    // Lecture包排课关联的课件id列表数据
    const {premium, tasks, snapshot}: any = await this.app.service('service-pack-user').Model.findById(packUser).select(['premium', 'tasks', 'snapshot'])
    if (Acan.isEmpty(tasks)) return {booking: _id, packUser, message: 'not found task in packUser'} // 没有课件需要排课
    const taskId = tasks.shift()
    const {linkSnapshot} = snapshot
    const task = linkSnapshot[taskId]
    if (!task) return {booking: _id, packUser, premium, taskId, message: 'not found task in service-auth'}
    await this.Model.updateOne({_id}, {$set: {packUserTasks: [taskId]}})
    await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, {$set: {tasks}})
    const rs = await this.importCreateSession(task, {
      students: [booker],
      uid: servicer,
      zoom: {passcode: true, waiting_room: true},
      sessionType: 'live',
      booking: _id,
      start,
      end,
    })
    let childs
    if (!Acan.isEmpty(task.link)) {
      // 创建子课件
      childs = await this.autoCreateLinkSession({
        pid: rs._id.toString(),
        booking: {_id, start, end, servicer, booker},
        linkSnapshot,
        unitSnapshot: task,
      })
    }
    return {...rs, childs}
  }
  // 获取管家服务关联预订数据
  async getByCarer({_id}: any, params: Params) {
    // 获取服务包封面
    const carerDoc: any = Acan.clone(await this.Model.findById(_id).select(['start', 'end', 'packUser', 'carer']))
    const carerPackUser: any = await this.app.service('service-pack-user').Model.findById(carerDoc.packUser).select(['snapshot'])
    const {curriculum, gradeGroup}: any = carerPackUser.snapshot
    // 获取管理的服务包 根据管家服务包的大纲，学科，年级过滤出关联的服务包
    const queryPackUser: any = {
      pid: {$exists: true},
      'snapshot.curriculum': curriculum,
      'snapshot.gradeGroup': {$in: gradeGroup},
    }
    if (carerDoc.carer?.subject) queryPackUser['snapshot.subject'] = carerDoc.carer.subject
    const packUsers = Acan.clone(await this.app.service('service-pack-user').Model.find(queryPackUser).select(['_']))
    const $in = packUsers.map((v: any) => v._id)
    logger.info('getByCarer', 'packUsers:', $in)
    // 获取服务包关联的订单，课堂已经关闭的, 未被管家服务过的
    const list = Acan.clone(await this.Model.find({packUser: {$in}, 'carer.used': false, 'session.status': 'close'}).select(['session', 'booker']))
    for (const o of list) {
      await this.extSession(o, params)
    }
    return list
  }
  // 管家服务import
  async getImportCarer({_id, bookings}: any, params: Params) {
    // 获取服务包封面
    const carerDoc: any = Acan.clone(await this.Model.findById(_id).select(['start', 'end', 'packUser', 'carer', 'servicer']))
    const carerPackUser: any = await this.app.service('service-pack-user').Model.findById(carerDoc.packUser).select(['snapshot'])
    const {name, cover}: any = carerPackUser.snapshot
    const bookingList: any = Acan.clone(await this.Model.find({_id: {$in: bookings}}).select(['session', 'booker']))
    if (Acan.isEmpty(bookingList)) return Promise.reject(new BadRequest('booking not found!'))
    const pages = []
    const questions: any = []
    const sessions = []
    for (const one of bookingList) {
      const id = one.session._id
      sessions.push(id)
      pages.push({_id: id, pic: one.image})
      questions.push({page: id, type: 'website', data: `/v2/account/takeaway/view/${id}?studentId=${one.booker}`})
    }
    const post = {
      type: 'bookingStuTask',
      name,
      image: cover,
      // unitType: '', // unit.type
      sessionType: 'live', // unit.sessionType
      zoom: {passcode: true, waiting_room: true},
      // category?: '', color?: '',
      // subjects?: [...],
      students: [bookings[0].booker], // session student user_id
      uid: carerDoc.servicer,
      pages, // slides.pages snapshot
      questions,
      booking: _id,
      start: carerDoc.start,
      end: carerDoc.end,
    }
    // 标记预订已经使用过管家服务
    await this.Model.updateMany({'session._id': {$in: sessions}}, {$set: {'carer.used': true}})
    // return post
    return await this.app.service('session').create(post, params)
  }
  // 通过新的预订获取Lecture服务包的最后一节结束的预订
  async getLectureLastEnd({_id}: any, params: Params) {
    const {packUser, start}: any = await this.Model.findById(_id).select(['packUser', 'start'])
    const lastQuery = {packUser, 'session.status': 'close', start: {$lt: start}}
    // console.log(lastQuery)
    const last = await this.Model.findOne(lastQuery).select(['_id'])
    if (!last) return null
    params.query = {}
    return await this.get(last._id.toString(), params)
  }

  // 替代find userQuery,可按邮箱/手机/classcipeId查询
  async getFind(query: any, params: Params) {
    let {userField, userFieldType, ['booker']: uid} = query || {}
    if (userField && userFieldType && !uid) {
      if (userFieldType === 'classcipeId') {
        userFieldType = 'email'
        userField = userField + '@classcipe.com'
      }
      let user = await this.app.service('users').Model.findOne({[userFieldType]: userField})
      if (!user) {
        return {total: 0, skip: 0, data: [], limit: 0}
      }
      query = {...query, ['booker']: user._id.toString()}
      delete query.userField
      delete query.userFieldType
    }
    const rs = {total: 0, limit: query.$limit || 10, skip: query.$skip || 0, data: []}
    const q = this.Model.find(query).limit(rs.limit).skip(rs.skip)
    rs.total = await this.Model.count(query)
    rs.data = await q.then()
    return rs
  }
  // 通过预订ID获取辅导课
  async getTutorialPackId({_id}: any, params: Params) {
    const doc: any = await this.Model.findById(_id).select('packUser')
    if (!doc) return Promise.reject(new BadRequest(`Not found Booking: ${_id}`))
    // 获取lecture服务包
    const pack: any = await this.app.service('service-pack-user').Model.findById(doc.packUser).select('pid uid premium snapshot._id')
    if (!pack?.pid) return Promise.reject(new BadRequest(`Not find lecture Pack: ${doc.packUser}`))
    if (!pack.premium) return Promise.reject(new BadRequest(`Not a lecture Pack: ${doc.packUser}`))
    // 获取lecture的主题包
    const ppack: any = await this.app.service('service-pack-user').Model.findById(pack.pid).select('snapshot.contentOrientated')
    if (!ppack?.snapshot?.contentOrientated) return Promise.reject(new BadRequest(`Not find Pack: ${pack.pid}`))
    // 获取lecture关联辅导包
    const contentOrientated = ppack.snapshot.contentOrientated.find((v: any) => v.premium == pack.premium)
    if (!contentOrientated?.servicePack) return Promise.reject(new BadRequest('Not find contentOrientated.servicePack'))
    // 获取 辅导包
    const rs: any = await this.app.service('service-pack-user').Model.findOne({uid: pack.uid, 'snapshot._id': contentOrientated.servicePack}).select('_id')
    if (!rs) return Promise.reject(new BadRequest(`Not find servicePackUser by Pack: ${contentOrientated.servicePack}`))
    return rs._id.toString()
  }
  // 自动排课的时候同时将课前课后也排课
  async autoCreateLinkSession({pid, linkSnapshot, unitSnapshot, booking}: any, params?: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const {linkGroup, link} = unitSnapshot
    if (Acan.isEmpty(link)) return // 没有课前课后
    const groups = linkGroup.map((v: any) => v._id)
    const linkMap: any = {}
    for (const l of link) {
      linkMap[l.id] = l.group
    }
    const childs: any = []
    for (const linkId in linkSnapshot) {
      if (!linkMap[linkId]) continue
      const {questions, materials, mode, name, cover, type, subjects}: any = linkSnapshot[linkId]
      for (const key of ['questions', 'materials']) {
        delete linkSnapshot[linkId][key]
      }
      const post: any = {
        pid,
        type: 'bookingStu' + mode.toFirstUpperCase(),
        sessionType: 'student',
        status: 'student',
        name,
        image: cover,
        unitType: type,
        subjects,
        students: [booking.booker],
        uid: booking.servicer,
        questions,
        materials,
        booking: booking._id,
        task: unitSnapshot,
      }
      if (groups.indexOf(linkMap[linkId]) === 0) {
        // 第一个组为 课前
        post.start = new Date()
        post.end = booking.start
      } else {
        post.start = booking.end
      }
      const doc = await this.app.service('session').create(post, {...params})
      const group = linkMap[linkId]
      const groupName = linkGroup.find((v: any) => v._id === group)
      childs.push({sid: doc._id, mode, sessionType: doc.sessionType, group, groupName})
      logger.log(childs)
      await this.app.service('session').Model.updateOne({_id: pid}, {$set: {childs, childSize: childs.length}}, options)
    }
    return childs
  }
  //通过 bookingid 批量获取服务包数据
  async getPackListByBookingIds({_id}: any) {
    const list: any = await this.Model.find({_id: {$in: _id}}).select('topic packUser')
    const packIds = list.map((v: any) => v.packUser)
    const packArr = Acan.clone(await this.app.service('service-pack-user').Model.find({_id: {$in: packIds}}, {pid: 1, snapshot: 1}))
    const packs: any = {}
    const pidMap: any = {}
    for (const o of packArr) {
      if (o.pid) pidMap[o._id] = o.pid
      else packs[o._id] = o.snapshot
    }
    // 如果存在 pid，则取父级服务包数据
    const pids = packArr.filter((v: any) => v.pid).map((v: any) => v.pid)
    if (!Acan.isEmpty(pids)) {
      const arr: any = await this.app.service('service-pack-user').Model.find({_id: {$in: pids}}, {snapshot: 1})
      for (const o of arr) {
        packs[o._id] = o.snapshot
      }
    }
    const data: any = {}
    for (const o of list) {
      const packUserId = pidMap[o.packUser] || o.packUser
      data[o._id] = {topic: o.topic, packInfo: packs[packUserId]}
    }
    return data
  }
}
