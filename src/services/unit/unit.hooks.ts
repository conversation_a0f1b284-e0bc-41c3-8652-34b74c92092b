import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
import hook from '../../hook'
import logger from '../../logger'
import {NotFound} from '@feathersjs/errors'
const search = require('feathers-mongodb-fuzzy-search')

export default {
  before: {
    all: [],
    find: [
      hook.sysQuery('uid', true, ['customer_service', 'customer_service_manager', 'academic_consultant', 'admin']),
      async (d: HookContext, con: any) => {
        const {_id: uid} = d.params.user ?? {}
        const query = d.params.query ?? {}
        query.$select = query.$select ?? d.service.selectList
        Object.assign(query, con)
        const {tab, $search} = query
        if (tab !== 'lib') await authenticate('jwt')(d)
        if ($search && Acan.isStr($search)) {
          query.$or = [{name: {$search}}]
          delete query.$search
        }
        const collabList: any = {}
        if (tab === 'share') {
          const collabCon: any = {uid, 'members.email': {$exists: true}}
          if (query.mode) collabCon.type = query.mode
          const rid = await d.app
            .service('collab')
            .Model.find(collabCon)
            .select('rid')
            .then((rs: any) => {
              return rs.map((v: any) => v.rid)
            })
          query._id = {$in: rid}
        } else if (tab === 'other') {
          const collabCon: any = {'members.email': d.params.user?.email}
          if (query.mode) collabCon.type = query.mode
          await d.app
            .service('collab')
            .Model.find(collabCon)
            .select(['rid', 'members.$'])
            .then((rs: any) => {
              return Acan.clone(rs).map((v: any) => (collabList[v.rid] = {collabId: v._id, ...v.members[0]}))
            })
          d.params.collabList = collabList
          query._id = {$in: Object.keys(collabList)}
          delete query.uid
        } else if (tab === 'me') {
          query.uid = uid
        } else if (tab === 'arch') {
          query.del = true
          query.uid = uid
        } else if (tab === 'published') {
          query.uid = uid
          query.$select = hook.selectLib(query.$select, d.service.publishKeys)
          query['publish.lib'] = true
          d.params.isLib = true
        } else if (tab === 'lib') {
          query['publish.lib'] = true
          query.$select = hook.selectLib(query.$select, d.service.publishKeys)
          d.params.isLib = true
          delete query.uid
        }
        if (!query.pid) query.pid = null
        query.del = !!query.del
        delete query.tab
        logger.query(query)
      },
      search({
        fields: ['name'],
      }),
    ],
    get: [
      hook.toClass,
      async (d: HookContext) => {
        if (hook.classExist(d)) return
        d.params.isLib = d.params.query?.isLib ?? false
        if (!Acan.isObjectId(d.id)) {
          // 通过slides.id查询
          d.result = await d.service.Model.findOne({sid: d.id})
        }
        return d
      },
    ],
    create: [
      authenticate('jwt'),
      async (d: HookContext) => {
        d.data.uid = d.params.user?._id
        await d.service.snapshotTpl(d.data, d.params)
        // Use fixed grouping in live task. #3385 #4731
        if (Acan.isEmpty(d.data.linkGroup) && ['task', 'pdTask'].includes(d.data.mode) && d.data.sessionType === 'live') {
          d.data.linkGroup = [{name: 'Preparations'}, {name: 'After class'}]
        }
        if (d.data.toolSource && d.data.mode === 'tool') await d.service.copyExt(d.data, d.params)
        if (d.data.toolSource && d.data.mode === 'pdTask') await d.service.copyService(d.data, d.params)
      },
    ],
    update: [authenticate('jwt'), hook.disable],
    patch: [
      authenticate('jwt'),
      hook.toClass,
      async (d: HookContext) => {
        if (hook.classExist(d)) return
        await d.service.snapshotTpl(d.data)
        if (d.data.$sync) {
          d.params.isSync = true
          delete d.data.$sync
        }
      },
    ],
    remove: [authenticate('jwt')],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        if (d.params.isLib) d.result.data = hook.resultLib(d.result.data, d.service.publishKeys)
        for (const v of d.result.data) {
          if (d.params.collabList?.[v._id]) v.collab = d.params.collabList?.[v._id]
        }
        await d.service.exts(d.result.data, d.params)
        await d.service.extTpl(d.result)
        if (isDev) d.result.query = d.params.query
        return d
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        if (!Acan.isObjectId(d.id)) return d // 通过 slide.id 查找数据
        if (!d.result?._id) return d
        const {_id, sid, snapshot, tpl, templateInfo, curriculum} = d.result
        // 自动将老数据的模板进行缓存
        if (Acan.isObjectId(_id) && tpl && curriculum !== 'pd' && !templateInfo?._id) {
          const tplPost: any = await d.app.service('unit-tpl').snapshot(tpl)
          if (!Acan.isEmpty(tplPost)) {
            logger.info('auto snapshot unit-tpl', _id)
            Object.assign(d.result, tplPost)
            await d.service.Model.updateOne({_id}, {$set: tplPost})
          }
        }
        if (d.params.isLib || !d.params.user) {
          if (!d.result?.snapshot) return Promise.reject(new NotFound('Not found publish'))
          d.result = {...d.result.snapshot, publish: d.result.publish, linkSnapshot: d.result.linkSnapshot}
          if (d.result) {
            await d.service.ext(d.result, d.params, true)
            await d.service.pagesLimit(d.result, d.params)
            if (d.params.user?._id) d.result.cart = await d.app.service('cart').Model.count({buyer: d.params.user._id, goodsId: _id})
          }
        } else {
          if (d.result) {
            await d.service.ext(d.result, d.params)
            await d.service.extSlides(d.result, d.params)
          }
          // 未初始化的复制出来的课件，保留快照返回
          if (!(sid?.includes('hash:') || sid === 'disable' || (!sid && snapshot))) delete d.result.snapshot
        }
        return d
      },
    ],
    create: [
      async (d: HookContext) => {
        if (d.data.toolSource) {
          await d.app.service('task-outline').copy([d.data.toolSource, d.result._id], d.params)
        }
      },
    ],
    update: [],
    patch: [
      (d: HookContext) => {
        if (hook.classExist(d)) return d
        const {_id, link} = d.result
        if (_id && link) {
          if (d.data.$addToSet?.link || d.data.$pull?.link) {
            d.service.Model.updateOne({_id}, {$set: {linkNum: link.length}}).then()
          }
        }
        if (d.params.isSync && _id) {
          const keys = Object.keys(d.data)
          const result: any = {_id: _id}
          for (const key of keys) {
            result[key] = d.result[key]
          }
          d.result = result
        }
        // for channels publish
        if (d.data.$pull || d.data.$addToSet) {
          const rs = {_id: d.id}
          for (const key in d.data.$pull || d.data.$addToSet) {
            Acan.objSet(rs, key, Acan.objGet(d.result, key))
          }
          d.result = rs
        } else {
          d.result = {_id: d.id, ...d.data}
        }
      },
    ],
    remove: [
      async (d: HookContext) => {
        const id = d.result._id.toString()
        const sid = d.result.sid
        // auto remove service-auth, 删除未通过认证的数据 https://github.com/zran-nz/bug/issues/4920
        // d.app.service('service-auth').Model.deleteOne({'unit._id': id}) // 删除精品课件认证数据
        await d.app.service('service-auth').Model.deleteOne({status: {$ne: 2}, 'unit._id': id})
        await d.app.service('service-auth').Model.deleteOne({status: {$ne: 2}, 'unitSnapshot._id': id})
        // auto remove order link and update archived status
        await d.app.service('order').Model.updateOne({'links.newId': id}, {$unset: {'links.$.newId': ''}, 'links.$.archived': true})
        // auto remove link content
        const list = await d.service.Model.find({'link.id': id}).select(['link'])
        for (const o of list) {
          for (const li of o.link) {
            if (li.id.toString() === id) {
              await d.service.Model.updateOne({_id: o._id}, {$pull: {link: {_id: li._id}}})
            }
          }
        }
        await d.app.service('collab').Model.deleteOne({rid: id})
        await d.app.service('task-outline').Model.deleteOne({task: id})
        if (sid && !sid.includes('hash:')) {
          await d.app.service('slides').Model.deleteOne({id: sid})
          const mrs = await d.app.service('materials').Model.deleteMany({id: sid})
          const qrs = await d.app.service('questions').Model.deleteMany({id: sid})
          console.log('delete questions: ', qrs, 'materials: ', mrs)
        }
      },
    ],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
