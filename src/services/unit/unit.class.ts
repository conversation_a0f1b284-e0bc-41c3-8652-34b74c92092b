import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'
import hook from '../../hook'
import {GeneralError, NotFound} from '@feathersjs/errors'

export class Unit extends Service {
  app: Application
  selectList: String[]
  publishKeys: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.selectList = [
      'cover',
      'name',
      'overview',
      'mode',
      'goals',
      'uid',
      'orderId',
      'guest',
      'unit',
      'type',
      'sid',
      'service',
      'sessionType',
      'subjects',
      'grades',
      'grade',
      'duration',
      'outlineSubjects',
      'pageNum',
      'curriculum',
      'idea',
      'inquiry',
      'tpl',
      'linkNum',
      'link',
      'linkGroup',
      'toolCount',
      'publish',
      'discount',
      'source',
      'sourceUid',
      'filled',
      'premiumAuth',
      'createdAt',
      'updatedAt',
      'del',
    ]
    this.publishKeys = this.selectList.filter((v) => v != 'publish')
  }
  libFindQuery(query: any) {
    const con: any = {'publish.lib': true}
    for (const key of Object.keys(query)) {
      if (key[0] === '$') continue
      else if (key === 'key') {
        query[key] = (query[key] || '').trim()
        if (query[key]) con.name = {$regex: query[key]}
      } else if (!query[key]) {
        continue
      } else {
        con[key] = query[key]
      }
    }
    return con
  }
  async libFind(query: any, params: Params, isList = false) {
    let {$limit = 10, $skip = 0, $sort = {_id: -1}}: any = query || {}
    $limit = parseInt($limit)
    $skip = parseInt($skip)
    const con = this.libFindQuery(query)
    const data: any = hook.resultLib(
      Acan.clone(await this.Model.find(con).select(hook.selectLib(this.selectList, this.publishKeys)).limit($limit).skip($skip).sort($sort)),
      this.publishKeys
    )
    await this.exts(data, params, true)
    if (!isList) return data
    return {total: await this.Model.count(con), limit: $limit, skip: $skip, data}
  }
  // 插件端获取带有ppt的课件列表
  async getAddonFind(query: any, params: Params) {
    let {$limit = 10, $skip = 0, $sort = {'snapshot.updatedAt': -1}}: any = query || {}
    $limit = parseInt($limit)
    $skip = parseInt($skip)
    const con = {uid: params.user?._id, mode: {$in: ['task', 'pdTask']}, pageNum: {$gt: 0}}
    // Acan.objClean(con)
    const data: any = Acan.clone(await this.Model.find(con).select(['sid', 'mode', 'cover', 'name', 'pageNum', 'uid']).limit($limit).skip($skip).sort($sort))
    await this.exts(data, params)
    return {total: await this.Model.count(con), limit: $limit, skip: $skip, data}
  }
  async getSearch({$limit = 3, key}: any, params: Params) {
    const latest = await this.libFind({mode: {$in: ['unit', 'task', 'pdUnit', 'pdTask', 'video']}, key, $limit}, params)
    const unit = await this.libFind({mode: 'unit', key, $limit}, params)
    const task = await this.libFind({mode: 'task', key, $limit}, params)
    const video = await this.libFind({mode: 'video', key, $limit}, params)
    const workshop = await this.app.service('session').getSearch({type: 'workshop', limit: $limit, key}, params)
    return {latest, unit, task, workshop, video}
  }
  async getLatestList(query: any, params: Params) {
    const mode = {$in: ['unit', 'task', 'pdUnit', 'pdTask', 'video']}
    return await this.libFind({mode, ...query, $sort: {'snapshot.updatedAt': -1}}, params, true)
  }
  async getTaskList(query: any, params: Params) {
    const mode = 'task'
    return await this.libFind({mode, ...query}, params, true)
  }
  async getUnitList(query: any, params: Params) {
    const mode = 'unit'
    return await this.libFind({mode, ...query}, params, true)
  }
  async getVideoList(query: any, params: Params) {
    const mode = 'video'
    return await this.libFind({mode, ...query}, params, true)
  }
  getRecommendIdea({key}: {key: String}, params: Params) {
    return this.Model.find({idea: {$regex: `(^${key})`, $options: 'i'}})
      .limit(100)
      .select(['idea', 'words'])
  }
  async getRecommendWords({key}: {key: String}, params: Params) {
    key = key.toLowerCase()
    const rs: any = await this.Model.distinct('words', {words: {$regex: `(^${key})`, $options: 'i'}})
    const list: any = {}
    for (const k of rs) {
      if (k.toLowerCase().includes(key)) list[k] = 1
    }
    return Object.keys(list)
  }
  async relateLinkList(doc: any, params: Params) {
    if (!doc || Acan.isEmpty(doc.link)) return []
    const idList = doc.link.map((v: any) => v.id)
    const list = Acan.clone(await this.Model.find({_id: {$in: idList}, del: false}).select(this.selectList))
    for (const one of list) {
      one.owner = await this.app.service('users').uidToInfo(one.uid)
      one.linkId = idList[one.id]
    }
    return list
  }
  async getLinksById({id, isLib}: any, params: Params) {
    let arr
    if (isLib) {
      const rs: any = await this.Model.find({_id: {$in: id}}).select(['snapshot.name'])
      arr = rs.map((v: any) => {
        return {_id: v._id, ...v.snapshot}
      })
    } else {
      arr = await this.Model.find({_id: {$in: id}}).select(['name'])
    }
    return arr
  }
  async getRelateLinkList({rid}: {rid: String}, params: Params) {
    const doc: any = await this.Model.findById(rid).select(['link'])
    return this.relateLinkList(doc, params)
  }
  async getAllRelateLinkList({rid}: any, params: Params) {
    const list: any = await this.Model.find({_id: {$in: rid}}).select(['link'])
    const rs: any = {}
    for (const one of list) {
      rs[one._id] = await this.relateLinkList(one, params)
    }
    return rs
  }
  async getRecommendInquiry({key}: {key: String}, params: Params) {
    const rs = await this.Model.find({inquiry: {$regex: `(^${key})`, $options: 'i'}})
      .limit(100)
      .select(['inquiry'])
    const list: any = {}
    if (rs)
      rs.forEach((v: any) => {
        v.inquiry.forEach((vc: string) => {
          list[vc] = 1
        })
      })
    return Object.keys(list)
  }
  getFindBySlideId({id}: any, params: Params) {
    return this.get(id, params)
  }
  async copyService(data: any, params: Params) {
    // tpl._id: tpl.tags, form toolSource task template
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const unit = Acan.clone(await this.Model.findById(data.toolSource, null, options).select(['service', 'subjects', 'outlineSubjects']))
    data.service = unit.service
    data.subjects = unit.subjects
    data.outlineSubjects = unit.outlineSubjects
    return data
  }
  async copyExt(data: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    // tpl._id: tpl.tags, form toolSource task template
    const unit = Acan.clone(await this.Model.findById(data.toolSource, null, options).select(['ext', 'template']))
    const ext: any = {}
    const tpl2Tag: any = {}
    for (const tpl of unit.template) {
      if (tpl.tags) tpl2Tag[tpl._id] = tpl.tags
    }
    // task.ext to new
    for (const key in unit.ext) {
      if (tpl2Tag[key]) {
        if (Array.isArray(unit.ext[key])) {
          ext[tpl2Tag[key]] = unit.ext[key]
        } else {
          const tmp: any = {}
          for (const kstr in unit.ext[key]) {
            const krr = kstr.split(':')
            if (tpl2Tag[krr[0]]) krr[0] = tpl2Tag[krr[0]]
            tmp[krr.join(':')] = unit.ext[key][kstr]
          }
          ext[tpl2Tag[key]] = tmp
        }
      }
    }
    data.ext = ext
    // logger.warn('task.ext -> ATool.ext:', tpl2Tag, ext, data.mode)
    return data
  }
  async getCopyTool({_id, unit, name}: any, params: Params) {
    const udoc: any = Acan.clone(await this.Model.findById(unit).select(['ext', 'template']))
    if (!udoc) return Promise.reject(new NotFound())
    const doc = _id ? Acan.clone(await this.Model.findById(_id)) : {mode: 'tool'}
    for (const key of ['_id', '__v']) delete doc[key]
    if (!doc.ext) doc.ext = {}
    Object.assign(doc.ext, udoc.ext)
    if (name) doc.name = name
    doc.toolSource = unit
    const ndoc = Acan.clone(await this.create(doc, params))
    // todo copy task-outline
    await this.app.service('task-outline').copy([unit, ndoc._id], params)
    return ndoc
  }
  getChild({pid}: {pid: string}, params: Params) {
    Object.assign(params.quary, {pid})
    return this.find(params)
  }
  async extLibrary(doc: any, params: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    doc.collectCount = await this.app.service('collect').Model.countDocuments({rid: doc._id}, options)
    const rs: any = await this.app.service('collect').Model.findOne({rid: doc._id, uid: params.user?._id}, null, options).select('_id')
    doc.collected = rs?._id
    // order ext
    doc.sales = await this.app.service('order').buyCount({id: doc._id, mode: doc.mode}, params)
    if (doc.uid !== params.user?._id) {
      doc.order = await this.app.service('order').buyInfo({id: doc._id, mode: doc.mode}, params)
    }
  }
  async exts(data: any, params: Params, isLib = false) {
    for (const one of data) {
      if (!one) continue
      await this.ext(one, params, isLib)
    }
    return data
  }
  async ext(result: any, params: Params, isLib = false) {
    result.owner = await this.app.service('users').uidToInfo(result.uid, params)
    if (isLib) await this.extLibrary(result, params)
    return result
  }
  async extTpl(result: any, params?: Params) {
    if (Acan.isEmpty(result.data)) return result
    for (const one of result.data) {
      if (!one) continue
      const rs: any = await this.app.service('unit-tpl').getName({_id: one.tpl}, params)
      if (!rs) continue
      one.tplName = rs.name
    }
    return result
  }
  isPublish(one: any) {
    return one.publish.lib || one.publish.link || one.publish.study
  }
  // 受限用户，限制ppt数量，隐藏sid
  async pagesLimit(one: any, params: Params, isSnapshot = false) {
    const isMe = params?.user?._id === one.uid
    if (Acan.isEmpty(one.pages)) return
    const pageNum = one.pageNum
    // 是否协同成员
    const isMember = await this.app.service('collab').isMember({rid: one._id}, params)
    // 是否后台管理员
    const isAdmin = hook.managerRoleHas(['admin', 'sales_manager', 'sales', 'customer_service_manager', 'customer_service', 'accountant'])({params})
    let end = 0
    if (isMe || isMember || isAdmin || !this.isPublish(one)) {
      end = pageNum
    } else {
      end = pageNum > 10 ? 3 : Math.ceil(pageNum * 0.3)
      delete one.sid // 隐藏sid
    }
    one.pages = isSnapshot || one.order ? one.pages : one.pages.slice(0, end)
    if (isDev)
      one.ext2 = {
        isSnapshot,
        isMe,
        isMember,
        uid: params?.user?._id,
        pageNum: one.pageNum,
        end,
      }
  }
  async extSlides(one: any, params: Params, isSnapshot = false) {
    if (!one?.sid) {
      // 有快照取快照里的数据
      if (one.snapshot) {
        one.pages = one.snapshot.pages
        return
      }
      return logger.log('extSlides no google.slides.id', one._id, one.type, one.name)
    }
    // 复制的课件还没初始化，直接取快照理的数据
    if (one.sid.includes('hash:') || one.sid === 'disable') {
      one.pages = one.snapshot?.pages || []
      return
    }
    let rs = await this.app.service('slides').getPagesBySlideId({id: one.sid.split(':').pop()}, params)
    if (!rs?.pages) return logger.log('extSlides no slides pages', one._id, one.type, one.name)
    one.pages = rs.pages
    one.pageNum = rs.pages.length
    await this.pagesLimit(one, params, isSnapshot)
  }
  // 对课件生成快照，同时下载ppt原件
  async snapshot({_id, set, school, classId, session}: any, params: Params) {
    const dbSession = params?.mongoose?.session
    const options = dbSession ? {session: dbSession} : {}
    const snapshot = Acan.clone(await this.app.service('unit').Model.findById(_id, null, options))
    if (!snapshot) return {status: false, message: 'snapshot error: not found', unit: _id, set}
    // 存在ppt, 并且是自己的课件, 并且在学校身份下排课，进行下载原件
    // 排课暂时不下载原件
    if (snapshot.sid && snapshot.uid === params.user?._id && (school || classId) && !session) {
      const rs = await this.app.service('slides').getSlideToS3({id: snapshot.sid, google: params.user?.google}, params)
      if (!rs?._id) return {status: false, message: 'Slides download failed', id: snapshot.sid, unit: snapshot._id, set}
    }
    // 扩展ppt快照数据
    if (snapshot.sid) await this.extSlides(snapshot, params, true)
    delete snapshot.snapshot
    delete snapshot.publish
    snapshot.outline = await this.app.service('task-outline').getByRid({_id}, params)
    snapshot.questions = Acan.clone(
      await this.app
        .service('questions')
        .Model.find({id: snapshot.sid}, null, options)
        .select([...this.app.service('questions').questionSelect, 'outlines'])
    )
    snapshot.materials = Acan.clone(await this.app.service('materials').Model.find({id: snapshot.sid}, null, options).select(['page', 'list']))
    snapshot.reflection = await this.app.service('reflection').unitSnapshot(_id, params)
    if (set) await this.Model.updateOne({_id}, {$set: {...set, snapshot}}, options)
    return snapshot
  }

  async patchUnPublish({_id}: any, params: Params) {
    const doc: any = Acan.clone(await this.Model.findById(_id).select(['link']))
    // 关联课件同时取消关联发布
    if (doc.link)
      for (const link of doc.link) {
        await this.Model.updateOne({_id: link.id}, {$set: {'publish.link': false, 'snapshot.publish.link': false}})
      }
    return await this.Model.updateOne({_id}, {$set: {'publish.lib': false, 'snapshot.publish.lib': false}})
  }
  async patchPublish(data: any, params: Params) {
    const {_id} = data
    const doc: any = Acan.clone(await this.Model.findById(_id).select(['sid', 'mode', 'source', 'link']))
    if (!doc) return {status: false, message: 'Not found'}
    if (doc.source) return {status: false, message: 'This is not original courseware'}
    if (Agl.taskMode.includes(doc.mode)) {
      if (!doc.sid) return {status: false, message: 'Not slides'}
      const rs = await this.app.service('slides').getSlideToS3({id: doc.sid, google: params.user?.google}, params)
      if (!rs?._id) return {status: false, message: 'Slides download failed', ...rs}
    }
    const $set: any = Acan.clone(data)
    delete $set._id
    delete $set.publish
    await this.Model.updateOne({_id}, {$set})
    // snapshot link contents
    const linkSnapshot: any = {}
    for (const link of doc.link) {
      const rs = await this.snapshot({_id: link.id}, params) // set: {'publish.link': true, 'publish.date': new Date()}
      if (rs.message) return rs // 关联课件报错
      linkSnapshot[rs._id] = rs
    }
    // publish to snapshot
    const rs = await this.snapshot({_id, set: {'publish.lib': true, 'publish.date': new Date(), linkSnapshot}}, params)
    return {status: true, ...rs}
  }
  async getRmAll({_id}: any, params: Params) {
    const one: any = await this.Model.findById(_id).select('link')
    for (const link of one.link) {
      await this.getRmAll({_id: link.id}, params)
    }
    return await this.Model.deleteOne({_id})
  }
  // 前端调用
  async patchCopy(data: any, params: Params) {
    const {_id} = data // orderId, name, tpl, curriculum, isLink
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const doc: any = await this.Model.findById(_id, null, options)
    if (!doc) return Promise.reject(new NotFound())
    return await this.copyFn({...data, doc}, params)
  }
  // nodejs 内部调用，仅在订单支付成功后
  async copy({_id, orderId, name, tpl, curriculum, isLink}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    console.log('copy_1')
    const doc: any = await this.Model.findById(_id, null, options)
    console.log('copy_2')
    if (!doc) return Promise.reject(new NotFound())
    // 销量+1
    console.log('unit copy', _id, doc.name, doc.publish.sales)
    await this.Model.updateOne({_id}, {$inc: {'publish.sales': 1}}, options)
    // await this.Model.updateOne({_id}, {$inc: {income: 6000}})
    console.log('copy_3')
    return await this.copyFn({_id, orderId, name, tpl, curriculum, isLink, doc}, params)
  }
  // 复制自己的课件，用 google drive api 复制
  async copyByDrive({name, sid}: any, params: Params) {
    const rs = await this.app.service('slides').getCopySlide({name, sid}, params)
    if (!rs.data?.id) return Promise.reject(new GeneralError(rs.message, rs.error))
    await this.app.service('slides').getSyncSlide({id: rs.data.id}, params)
    return rs.data.id
  }
  // 复制逻辑
  async copyFn({_id, orderId, name, tpl, curriculum, isLink, doc}: any, params: Params) {
    const uid = params.user?._id.toString()
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    // 存在订单，从快照复制，不存在则直接复制
    if (orderId && !doc.snapshot) {
      return Promise.reject(new GeneralError(`${isLink ? 'link._id' : 'unit._id'}: ${_id}, no snapshot, order: ${orderId}`))
    }
    const post: any = Acan.clone(orderId ? doc.snapshot : doc)
    delete post.sid
    if (orderId) post.orderId = orderId
    if (!Acan.isEmpty(post.link)) {
      let rs: any
      // 如果有子课件，则复制子课件
      for (const o of post.link) {
        if (orderId) {
          // 订单从快照中复制
          const snapshot = doc.linkSnapshot[o.id]
          if (!snapshot) return Promise.reject(new GeneralError(`link._id: ${o.id}, no snapshot, order: ${orderId}, parent._id: ${doc._id}`))
          rs = await this.copyFn({_id: o.id, orderId, isLink: true, doc: {...snapshot, snapshot, linkSnapshot: doc.linkSnapshot}}, params)
        } else {
          // 非订单从源头复制
          rs = await this.patchCopy({_id: o.id, isLink: true}, params)
        }
        o.id = rs._id
      }
    }
    if (['task', 'pdTask'].includes(doc.mode) && doc.sid) {
      if (doc.sid.includes('hash:')) {
        // 购买的课件未初始化再次复制
        post.sid = doc.sid
      } else if (orderId) {
        // 使用hash对应的原件复制 用于library购买的课件
        let {hash}: any = (await this.app.service('slides').Model.findOne({id: doc.sid}, null, options).select('hash')) || {}
        post.sid = `hash:${hash}:${doc.sid}`
        doc.snapshot.publish = {}
        post.snapshot = doc.snapshot
      } else if (doc.uid !== uid || doc.sid === 'disable') {
        // 协同课件不能复制slide, 协同课件二次复制
        delete post.snapshot
        post.sid = 'disable'
        post.snapshot = await this.snapshot({_id: doc._id}, params)
      } else {
        // 自己的课件，通过谷歌drive api 复制
        delete post.snapshot
        post.sid = await this.copyByDrive({sid: doc.sid, name}, params)
      }
    }
    // buy or copy from library
    if (post.uid !== uid) {
      if (!post.sourceUid) post.sourceUid = post.uid
      post.source = post._id
    }
    post.publish = {}
    post.name = name || post.name
    post.curriculum = curriculum || post.curriculum
    post.tpl = tpl || post.tpl
    ;['_id', '__v', 'price', 'discount', 'createdAt', 'updatedAt', 'uid', 'isEdit', 'oldId'].map((k) => {
      delete post[k]
    })
    console.log('CREATE_0', typeof params.mongoose?.session)
    const ndoc = Acan.clone(await this.create(post, {...params, mongoose: {session: session}}))
    console.log('CREATE_1', typeof params.mongoose?.session)
    logger.info('复制课件成功: ', post.name, ndoc._id)
    if (orderId && !isLink) {
      // 更新订单最新id与hash,archived
      const $set: any = {'links.$.archived': false}
      // if (post.sid) $set['links.$.hash'] = post.sid
      const rs = await this.app.service('order').Model.updateOne({_id: orderId, 'links.id': _id}, {$set}, options)
      // logger.info('更新订单成功', rs, {orderId, 'links.id': _id}, $set)
    }
    console.log('post.toolSource', post.toolSource, _id, ndoc._id)
    if (!post.toolSource) {
      await this.app.service('task-outline').copy([_id, ndoc._id], params)
      logger.info('更新tool outline')
    }
    return ndoc
  }
  async snapshotTpl(data: any, params?: Params) {
    if (!data?.tpl) return
    Object.assign(data, await this.app.service('unit-tpl').snapshot(data.tpl, params))
  }
  async getListByIds({ids}: any, params: Params) {
    let list: Array<any> = await this.Model.find({_id: {$in: ids}})
      .select(this.selectList)
      .lean()
    return await this.exts(list, params)
  }
  async getLiveLinkNum({_id}: any, params: Params) {
    const {link}: any = await this.Model.findById(_id).select('link')
    return await this.Model.count({_id: {$in: link.map((v: any) => v.id)}, sessionType: 'live'})
  }
}
