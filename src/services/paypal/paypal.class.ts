import {Id, NullableId, Paginated, Params, ServiceMethods} from '@feathersjs/feathers'
import {Application} from '../../declarations'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')
import axios from 'axios'
const paypalSettings = require('../../../config/paypal.json')
const paypalSetting = !isDev ? paypalSettings.live : paypalSettings.sandbox

interface Data {}

interface ServiceOptions {}

export class Paypal implements ServiceMethods<Data> {
  app: Application
  options: ServiceOptions

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options
    this.app = app
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return []
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id,
      text: `A new message with ID: ${id}!`,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return {id}
  }

  // async getTest({id}: {id: string; session: any}, params: Params): Promise<Data> {
  //   const mongooseClient = this.app.get('mongooseClient')
  //   const session = await mongooseClient.startSession()
  //   try {
  //     await session.withTransaction(async () => {
  //       await this.app.service('log').create({uid: '658c1e451857ef1e012846c3'}, {session})
  //       // throw new Error('test')

  //       await this.app.service('log').create({uid: '658c1e451857ef1e012846c3'}, {session})
  //     })
  //   } catch (error) {
  //     console.log(error)
  //     // await session.abortTransaction()
  //   } finally {
  //     await session.endSession()
  //   }
  //   // try {
  //   //   // await session.startTransaction()
  //   //   // let a = await this.app.service('sales-follow-up').Model.create(
  //   //   //   {
  //   //   //     customer: '658c1e451857ef1e012846c3',
  //   //   //     servicePackUser: '658c1e451857ef1e012846c3',
  //   //   //     servicePack: '658c1e451857ef1e012846c3',
  //   //   //   },
  //   //   //   {session}
  //   //   // )
  //   //     let a: any = await this.app.service('order').patch('684d21ded1ef907c01d4a58c', {$set: {point: 33},{mongodb: {session}}})
  //   //     // console.log('🚀 ~ getTest ~ a:', a)
  //   //     // throw new Error('test')
  //   //     await this.app.service('order').Model.updateOne({_id: '684d21ded1ef907c01d4a58c'}, {$set: {reminder: 33}}, {session})
  //   //     await session.commitTransaction()
  //   // } catch (error) {
  //   //   console.log('🚀 ~ getTest ~ error:', error)
  //   //   await session.abortTransaction()
  //   // } finally {
  //   //   await session.endSession()
  //   // }
  //   // let result = await this.app.service('session').Model.findOne({_id: '6842c4f2965f28c7f12921b9'})
  //   // let b = await this.app.service('session').sendEndNotice(result, params)
  //   // let b = await this.app.service('notice-tpl').mailto('MessageToUserRegardingPremiumContentVerification', '<EMAIL>', {}, params.user?._id)
  //   // let b = await this.app.service('session').cron1({}, params)
  //   // let b = await this.app.service('session').substitutePush(params)
  //   // let b = await this.app.service('order').get('676673971daa4f2b1431d7ed')
  //   // let b = await this.app.service('order').autoSeparate()
  //   // let session = await this.app.service('session').Model.findOne({_id: '67480d8229a43d6993cf0d2c'})
  //   // let packUser = await this.app.service('service-pack-user').Model.findOne({_id: '66828078f6ed8abb69753cbb'})
  //   // let b = await this.app.service('suspend-class').suspend({type: '0-satisfaction-rate', days: 1, uid: '658c1e451857ef1e012846c3'})
  //   // let b: any = await this.app.service('suspend-class').Model.findOne({uid: '658c1e451857ef1e012846c3'}).sort({endAt: 1})
  //   // console.log('🚀 ~ getTest ~ b:', b)
  //   // let b = await this.app.service('session').patchNewPrompt({_id: '65f2a49a115c60f9cffc023e'}, params)
  //   // let b = await this.app.service('users').Model.updateOne({_id: '664afc590a52005e132709171'}, {point: 2})
  //   return {}
  // }

  // getMail({id}: any, params?: Params) {
  //   let user = {
  //     _id: '658c1e451857ef1e012846c3',
  //     email: '<EMAIL>',
  //   }
  //   this.app
  //     .service('notice-tpl')
  //     .send('OrderPaySuccess', {_id: user._id, email: user.email}, {name: `test${Math.floor(Math.random() * (1000 - 100) + 100)}`, url: 'url123'})
  //   return user
  // }
  // Return order id and Pull up the payment desk
  // TODO gift card
  async getPayment({id}: {id: string}, params: Params): Promise<Data> {
    const order: any = await this.app.service('order').Model.findOne({_id: id})

    // 检查主题服务包
    // if (checkPremium && order.type == 'service_premium') {
    //   let servicePack: any = await this.app.service('service-pack').Model.findOne({_id: order.servicePremium})
    //   if (!servicePack || new Date(servicePack.updatedAt).getTime() != new Date(order.servicePremiumSnapshot.updatedAt).getTime()) {
    //     return Promise.reject(new BadRequest('Service pack is changed'))
    //   }
    // }

    let links = order.links.filter((item: any) => !item.removed)
    if (links.length === 0) {
      return Promise.reject(new GeneralError('Links is empty'))
    }
    if (order.status !== 100) {
      return Promise.reject(new GeneralError('Order status is not 100'))
    }
    let res = await this.createPaypalOrder(order.price, order._id)
    console.log('Res==>', res.status)
    if (res.status === 'CREATED') {
      return {
        success: true,
        ...res,
      }
    } else {
      return Promise.reject(new GeneralError('Paypal created err'))
    }
  }

  // Get paypal access token
  async getRequestToken() {
    const authorization = Buffer.from(`${paypalSetting.client_id}:${paypalSetting.client_secret}`).toString('base64')
    const redis = this.app.get('redis')
    let token = await redis.get('paypal_token')
    if (token) {
      return token
    }

    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: `${paypalSetting.host}/v1/oauth2/token?grant_type=client_credentials`,
        headers: {
          Accept: 'application/json',
          'Accept-Language': 'en_US',
          Authorization: 'Basic ' + authorization,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      })
        .then(async (res) => {
          let {access_token, expires_in} = res.data
          await redis.set('paypal_token', access_token)
          await redis.EXPIRE('paypal_token', expires_in - 100)
          resolve(access_token)
        })
        .catch(function (err) {
          reject(err.response.data.error_description)
        })
    })
  }

  // Create paypal order
  async createPaypalOrder(amount: string, invoice_id: string): Promise<any> {
    let token = await this.getRequestToken()
    let price = (parseFloat(amount) / 100).toFixed(2)
    return axios({
      method: 'post',
      url: `${paypalSetting.host}/v2/checkout/orders`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: {
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: price,
              // breakdown: {
              //   item_total: {
              //     currency_code: 'USD',
              //     value: amount,
              //   },
              //   // discount: {
              //   //   currency_code: 'USD',
              //   //   value: '20',
              //   // },
              // },
            },
            // items: [
            //   {
            //     name: 'class',
            //     quantity: '1',
            //     unit_amount: {
            //       currency_code: 'USD',
            //       value: amount,
            //     },
            //   },
            // ],
            invoice_id: invoice_id,
            // custom_id: invoice_id,
            // reference_id: invoice_id,
            description: 'full payment',
          },
        ],
        intent: 'CAPTURE',
      },
    })
      .then((res) => {
        return res.data
      })
      .catch((err) => {
        return err.response.data
      })
  }

  // Paypal refund
  async getRefund({id, amount}: any, params: Params): Promise<any> {
    try {
      let result: any = await this.paypalRefundRequest(id, amount)
      if (result.status === 'COMPLETED') {
        return {
          success: true,
          message: 'refund succeeded',
        }
      } else {
        return {
          success: false,
          message: 'refund failed',
        }
      }
    } catch (err) {
      return {
        success: false,
        message: err,
      }
    }
  }

  // Paypal refund request
  async paypalRefundRequest(capture_id: string, amount: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: `${paypalSetting.host}/v2/payments/captures/${capture_id}/refund`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
        data: JSON.stringify({
          amount: {value: amount, currency_code: 'USD'},
        }),
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }
  // Show captured payment details
  async paypalCapturesRequest(capture_id: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: `${paypalSetting.host}/v2/payments/captures/${capture_id}`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }
  // Show order details
  async paypalOrderRequest(id: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: `${paypalSetting.host}/v2/checkout/orders/${id}`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }

  // Check if refund already exists for given capture and amount
  async checkRefundExists(capture_id: string, expectedAmount: number): Promise<{exists: boolean; refundId?: string}> {
    try {
      const captureDetails: any = await this.paypalCapturesRequest(capture_id)

      // Check if there are any refunds for this capture
      if (captureDetails.refunds && captureDetails.refunds.length > 0) {
        const expectedAmountStr = (expectedAmount / 100).toFixed(2)

        // Look for a completed refund with matching amount
        for (const refund of captureDetails.refunds) {
          if (refund.status === 'COMPLETED' && refund.amount && refund.amount.value === expectedAmountStr) {
            return {
              exists: true,
              refundId: refund.id,
            }
          }
        }
      }

      return {exists: false}
    } catch (error) {
      console.error('Error checking PayPal refund status:', error)
      // If we can't check, assume refund doesn't exist to be safe
      return {exists: false}
    }
  }
}
