import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'

export class TaskOutline extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async getByRid({_id}: {_id: string}, params: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    return Acan.clone(await this.Model.findOne({task: _id}), null, options)
  }
  async copy([old, nid]: any, params: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const doc = Acan.clone(await this.Model.findOne({task: old}), null, options)
    if (!doc) return {}
    for (const k of ['_id', '__v']) {
      delete doc[k]
    }
    doc.task = nid
    return await this.Model.create(doc, params)
  }
  // 2025-06-21 之后的版本可以弃用
  async unitServiceSet(task: string, data: any, $set: any, params?: Params): Promise<void> {
    let pdKey = null
    for (const key of Object.keys(data)) {
      if (key.includes('.curr') && data[key] === 'pd') pdKey = key.split('.')[0]
    }
    if (!pdKey) return
    const subjectIds = []
    for (const key of Object.keys(data[`${pdKey}.data`])) {
      const _id = key.split(':')?.[1]
      if (_id) subjectIds.push(_id)
    }
    if (Acan.isEmpty(subjectIds)) return logger.info('no subjectId', subjectIds)
    $set['service.type'] = subjectIds
    $set['service.participants'] = await this.app.service('subjects').idToParticipants(subjectIds, params)
    logger.info(task, $set)
    if (!Acan.isEmpty($set)) await this.app.service('unit').Model.updateOne({_id: task}, {$set})
  }
}
