import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'
const got = require('got')
const bcrypt = require('bcryptjs')
const {GeneralError, BadRequest} = require('@feathersjs/errors')

import {FeathersError} from '@feathersjs/errors'
class PlanLimit extends FeathersError {
  constructor(message: string, name?: string, data?: any) {
    super(message, name || 'PlanLimit', 451, 'PlanLimit', data)
  }
}
export class Users extends Service {
  app: Application
  selectList: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    // 普通用户可见属性
    this.selectList = ['name', 'nickname', 'avatar', 'timeZone', 'tz', 'roles', 'lang', 'inviteCode', 'email', 'createdAt']
    this.app = app
  }
  // 查找用户谷歌登录的谷歌邮箱
  async getGoogleEmail({}: any, params: Params) {
    const {google} = params.user ?? {}
    if (!google) return null
    let ut: any = (await this.app.service('user-token').Model.findOne({sub: google}).select(['email', 'sub', 'avatar'])) ?? {}
    return ut
  }
  // addon 通过key获取登陆token
  async getAddonKey({key}: any) {
    const rs = await this.app.get('redis').get(`addonKey:${key}`)
    return rs
  }
  // 更新账号的邮箱为谷歌邮箱
  async getGoogleEmailSync({google}: any, params: Params) {
    if (!google) google = params.user?.google
    if (!google) return Promise.reject(new GeneralError('no found google account'))
    let ut: any = (await this.app.service('user-token').Model.findOne({sub: google}).select(['email', 'sub', 'avatar'])) ?? {}
    if (!ut.email) return Promise.reject(new GeneralError('no found google email!'))
    await this.Model.updateOne({google}, {$set: {email: ut.email}})
    return ut
  }
  async mailReg(data: any, params: Params): Promise<any> {
    if (data.roles.includes('student')) {
      return this.app.service('notice-tpl').mailto('SignUp', data.email, data)
    } else {
      const schoolData = this.app.get('trainingSchool')
      const key = await this.app.service('school-user').getLinkKey({school: schoolData._id}, params)
      const url = `${SiteUrl}/v2/account/schoolApply/${key}?notice_time=1`
      return this.app.service('notice-tpl').mailto('SignUpTeacher', data.email, {username: data.name.join(' '), url})
    }
  }
  async getTeacherSignUpMail({}: any, params: Params): Promise<any> {
    const {email} = params.user ?? {}
    const schoolData = this.app.get('trainingSchool')
    const url = `${SiteUrl}/v2/home/<USER>
    return this.app.service('notice-tpl').mailto('ClasscipeTrainingCenterIsApproved', email, {url})
  }
  async redisHcache(key: string, hash: object | string, dbGet: any) {
    const redisClient = this.app.get('redis')
    let json = await redisClient.HGET(key, hash)
    if (!Acan.isEmpty(json)) return JSON.parse(json)
    logger.info('redisHcache from db', key, hash)
    const rs = await dbGet()
    if (!rs) return rs
    if (typeof hash === 'object') hash = hash.toString()
    redisClient.HSET(key, hash, JSON.stringify(rs))
    return rs
  }
  async getStat(query: any, params: Params) {
    const rs: any = {}
    rs.users = await this.Model.count()
    rs.schoolGroup = await this.app.service('school-plan').Model.aggregate([
      {
        $group: {
          _id: {
            status: '$status',
            personal: '$personal',
          },
          count: {$sum: 1},
        },
      },
      {
        $sort: {_id: 1},
      },
    ])
    return rs
  }
  async emailList(arr: string[], params?: Params) {
    const rs = await this.Model.find({_id: {$in: arr}}).select(['email'])
    const list: any = {}
    rs.map((v: any) => {
      list[v._id] = v.email
    })
    return list
  }
  async getGoogle({_id}: any, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const rs: any = await this.Model.findOne({_id}, null, options).select(['google'])
    return rs?.google
  }
  async emailToUid(email: string) {
    return await this.redisHcache('email:_id', email, async () => {
      let rs = Acan.clone(await this.Model.findOne({email}).select(['email']))
      return rs?._id
    })
  }
  async emailToInfo(email: string) {
    return await this.uidToInfo(await this.emailToUid(email))
  }
  async byMail(email: string) {
    return await this.redisHcache('email:user', email, async () => {
      return Acan.clone(await this.Model.findOne({email}).select(['email', 'nickname', 'avatar']))
    })
  }
  async uidToId(uid: string) {
    return await this.redisHcache('uid:_id', uid, async () => {
      let rs = Acan.clone(await this.Model.findOne({uid}).select(['_id']))
      return rs?._id
    })
  }
  async uidToInfo(_id: string, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    _id = Acan.clone(_id)
    // uid = users._id
    const select = ['name', 'nickname', 'avatar', 'email', 'lang', 'timeZone', 'tz', 'inviteCode', 'inviter', 'roles', 'mobile']
    if (!Acan.isObjectId(_id)) return {}
    return await this.redisHcache('_id:info', _id, async () => {
      const rs: any = Acan.clone(await this.app.service('users').Model.findById(_id, null, options).select(select))
      if (!rs) return null
      rs.uid = rs._id
      return rs
    })
  }
  async extOwner(arr: any) {
    const list: any = {}
    for (const v of arr) {
      if (!v) continue
      if (list[v.uid]) v.owner = list[v.uid]
      else v.owner = list[v.uid] = await this.uidToInfo(v.uid)
    }
  }
  async checkPlanLimit({school, num, type = 'teacher'}: any, params: Params): Promise<any> {
    school = school || params.user?._id
    const plan: any = await this.app.service('users').getAccountPlan({school}, params)
    const typeMap: any = {teacher: 'TeacherLimit', student: 'StudentLimit'}
    if (!plan[type] || plan[type] < plan.count[type] + num)
      return await Promise.reject(
        new PlanLimit(`You have reached the upper limit of the ${type}.`, typeMap[type], {school, plan: plan[type], [type]: plan.count[type], num})
      )
    // return (d.result = {message: , code: ''}), d
  }
  async getAccountPlan({school}: {school: string}, params: Params) {
    const uid = params.user?._id
    school = school || uid
    // if (!school) return {space: 500 * 1024 * 1024}
    let rs: any = await this.app.service('school-plan').Model.findOne({_id: school})
    // 自动创建个人学校
    if (!rs && school === uid) {
      rs = await this.app.service('school-plan').Model.create({_id: uid, name: params.user?.name?.join(' ') || '', personal: true})
    }
    return (
      rs || {
        teacher: 1,
        student: 10,
        space: 1000 * 1024 * 1024,
      }
    )
  }
  async getAccountStat({school}: {school: string}, params: Params) {
    school = school || params.user?._id
    const rs = await this.app.get('redisHCache')(school ? 'StatSchool' : 'StatUser', school || params.user?._id, async () => {
      return await this.getCountStat({school}, params)
    })
    // todo new count
    // rs.classes = await this.getOldCountClass({school}, params)
    if (!school) return rs
    return rs
  }
  async getCountStat({school}: {school?: string}, params: Params): Promise<any> {
    school = school || params.user?._id
    const data: any = {curriculum: 0}
    let crs: any
    if (school) {
      data.teacher = await this.app.service('school-user').Model.count({school, del: false})
      data.student = await this.app.service('students').Model.count({school, del: false})
      crs = await this.app.service('conf-school').Model.findOne({rid: school, key: 'Curriculum'}).select(['val'])
    } else {
      crs = await this.app.service('conf-user').Model.findOne({uid: params.user?._id, key: 'Curriculum'}).select(['val'])
    }
    if (!Acan.isEmpty(crs?.val?.curriculum) && crs?.val?.subjects) {
      for (const key of Object.keys(crs.val.subjects)) {
        if (Acan.isEmpty(crs.val.subjects[key])) continue
        data.curriculum++
      }
    }
    // logger.warn(school, data.curriculum, crs?.val)
    return data
  }
  async findByMail(email: string) {
    let rs = await this.app.service('users').Model.findOne({email})
    if (rs) return Acan.clone(rs)
    return null
  }
  async patchSetAdmin({email}: {email: string}, params: Params): Promise<any> {
    const user = await this.findByMail(email)
    if (!user) return null
    return await this.patch(user._id, {$addToSet: {roles: 'admin'}})
  }
  getCheckAccount(con: any, params: Params) {
    Acan.objClean(con)
    if (Acan.isEmpty(con)) return 0
    return this.Model.count(con)
  }
  // 验证码判断
  async checkCaptcha(data: any, isDel = false) {
    const {mobile, email, emergencyContact, id, captcha} = data
    // 学生账号创建的时候忽略, 谷歌登录注册的时候忽略
    if (data.studentId || data.google) return true
    // 验证码不存在，禁止更新 密码/手机/邮箱/紧急联系人 #4848
    if (!captcha) {
      for (const key of ['mobile', 'password', 'email', 'emergencyContact', 'id']) {
        delete data[key]
      }
      return false
    }
    const redis = this.app.get('redis')
    const ckey = `Captcha:${email || mobile || emergencyContact || id}`
    const code = await redis.get(ckey)
    if (code !== captcha) return Promise.reject(new GeneralError('captcha error', {data}))
    if (isDel) await redis.del(ckey)
    return true
  }
  // 验证码检查
  async getCheckCaptcha(query: any, params: Params) {
    if (!query.captcha) return Promise.reject(new Error('Captcha isEmpty'))
    return await this.checkCaptcha(query)
  }
  // 按天统计数据
  async incrbyDay(key: String) {
    const redis = this.app.get('redis')
    return await redis.HINCRBY(Acan.time('Ymd'), key, 1)
  }
  // 按小时统计数据
  async incrbyHour(key: String) {
    const redis = this.app.get('redis')
    return await redis.HINCRBY(Acan.time('YmdH'), key, 1)
  }
  async getCaptcha({mobile, email, emergencyContact}: any, params: Params) {
    const redis = this.app.get('redis')
    const key = email || mobile || emergencyContact
    if (!key) return Promise.reject(new Error('empty mobile or email'))
    const ckey = `Captcha:${key}`
    // 限制账号次数
    if (mobile || emergencyContact) {
      const count = await this.incrbyDay(key)
      // 限制8次/天 #4887
      if (count > 8) return Promise.reject(new BadRequest('RequestFrequencyLimit', {mobile}))
    }
    // 限制ip次数, 每小时5次
    // const count = await incrbyHour(params.Aip)
    // if (count > 10) return {code: 'RequestFrequencyLimit', key: params.Aip}
    const code = Acan.random(100000, 999999)
    redis.set(ckey, code)
    redis.EXPIRE(ckey, 900)
    let rs: any = {}
    if (mobile || emergencyContact) {
      let ubj = new URL(this.app.get('api3Url') + '/sms/captcha')
      if (key.indexOf('+86') == 0 || key.indexOf('86') == 0) {
        ubj = new URL(this.app.get('api3Url') + '/tencent-sms/captcha')
      }
      ubj.searchParams.set('mobile', key)
      ubj.searchParams.set('code', code)
      rs = (await got(ubj.href, {json: true})).body
    } else {
      rs = await this.app.service('notice-tpl').mailto('Captcha', email, {code})
    }
    if (isDev) rs.code = code
    return rs
  }
  async getCaptchaById({id}: any, params: Params) {
    const redis = this.app.get('redis')
    const key = id
    if (!key) return Promise.reject(new Error('empty id'))
    const ckey = `Captcha:${key}`
    const code = Acan.random(100000, 999999)
    redis.set(ckey, code)
    redis.EXPIRE(ckey, 900)
    let rs: any = {}

    let studentData: any = await this.app.service('students').Model.findOne({id: key})
    if (studentData?.parent?.email) {
      rs = await this.app.service('notice-tpl').mailto('Captcha', studentData.parent.email, {code})
    }
    if (isDev) rs.code = code
    return rs
  }
  // 通过验证码 重置密码
  async patchForgetPassword({mobile, email, captcha, password}: any, params: Params) {
    if (!mobile && !email) return Promise.reject(new Error('mobile or email empty'))
    if (!captcha) return Promise.reject(new Error('captcha empty'))
    if (!password) return Promise.reject(new Error('password empty'))
    const isOk = await this.checkCaptcha({mobile, email, captcha}, true)
    if (!isOk) return Promise.reject(new Error('captcha error'))
    const doc = await this.Model.findOne(mobile ? {mobile} : {email})
    if (!doc) return Promise.reject(new Error('user not found'))
    this.send(doc, 'ResetPasswordSuccessfully', params)
    return await this.Model.updateOne({_id: doc._id}, {password})
  }
  // 通过旧密码 重置密码
  async patchChangePassword({oldPassword, password}: any, params: Params) {
    const doc: any = await this.Model.findById(params.user?._id)
    if (!doc) return Promise.reject(new Error('user not found'))
    if (!bcrypt.compareSync(oldPassword, doc.password)) return Promise.reject(new GeneralError({param: 'oldPassword', message: '原密码错误'}))
    return await this.Model.updateOne({_id: doc._id}, {password})
  }
  async nameFormatter({user, uid}: any, params?: Params): Promise<string> {
    if (!user) {
      user = await this.uidToInfo(uid, params)
    }
    const name = user?.name ?? []
    const nickname = user?.nickname ?? ''
    const firstname = user?.firstname ?? ''
    const lastname = user?.lastname ?? ''
    const email = user?.email ?? ''

    // name first
    if (name && name?.length) {
      if (Array.isArray(name)) return name.map((e) => (e || '').toFirstUpperCase()).join(' ')
      else if (typeof name === 'string') return name.toFirstUpperCase()
    }

    // old nickname
    if (nickname) return nickname.toFirstUpperCase()
    if (lastname && firstname) return `${firstname} ${lastname}`
    if (firstname || lastname) return firstname || lastname
    if (email) return email
    return 'Unknown Name'
  }

  // 生成邀请码 之后去除该方法
  async getGenerateInviteCode(data: any, params: Params): Promise<void> {
    let skip = 0
    let limit = 1000
    let users: any = await this.Model.find().skip(skip).limit(limit)
    while (users.length) {
      for (const user of users) {
        if (!user.inviteCode) {
          user.inviteCode = Math.round(Date.now() / 1000).toString(36) + Acan.random(1000, 9999).toString(36)
          user.save()
        }
      }
      skip += limit
      users = await this.Model.find().skip(skip).limit(limit)
    }
  }
  getRolesGroup({}: any, params: Params) {
    return this.Model.aggregate([
      {$match: {}},
      {
        $group: {_id: '$roles', total: {$sum: 1}},
      },
      {$sort: {total: -1}},
    ])
  }
  getStudentId({studentId}: any) {
    return this.Model.findOne({studentId}).select(['studentId', 'name', 'avatar', 'nickname', 'email', 'mobile', 'roles'])
  }

  async send(doc: any, tpl: string, params: Params): Promise<any> {
    let {name, email} = doc
    return await this.app.service('notice-tpl').mailto(tpl, email, {username: name.join(' ')}, params.user?._id)
  }
}
