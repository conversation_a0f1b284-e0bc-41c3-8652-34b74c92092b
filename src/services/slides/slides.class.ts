import {Service, MongooseServiceOptions} from 'feathers-mongoose'
const got = require('got')

import {Params} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import logger from '../../logger'
const {google} = require('googleapis')

import {FeathersError, GeneralError} from '@feathersjs/errors'
class GoogleNoPermission extends FeathersError {
  constructor(message: string, data?: any, status = 431) {
    super(message, 'GoogleNoPermission', status, 'google-no-permission', data)
  }
}

export class Slides extends Service {
  app: Application
  authConf: any
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.authConf = app.get('googleAuth')
  }
  async getIdByTask({task}: {task: string}, params: Params) {
    const rs: any = await this.Model.findOne({task}).select('id')
    return rs.id
  }
  async getBySlideId({task, id}: {task: string; id: string}, params: Params) {
    let rs = await this.Model.findOne({id}).sort({updatedAt: -1})
    if (!rs && !task) return rs
    if (!rs && task) rs = await this.create({id, task})
    return this.picFormat(Acan.clone(rs))
  }
  async getPagesBySid({sid}: any) {
    const rs = Acan.clone(await this.Model.findOne({id: sid}).sort({updatedAt: -1}).select('pages'))
    // this.urlToS3(rs)
    return rs
  }
  async getPagesBySlideId({id, rev}: {id: string; rev?: string}, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const rs = Acan.clone(await this.Model.findOne({id}, null, options).sort({updatedAt: -1}).select(['pages', 'rev']))
    // this.urlToS3(rs)
    return rs
  }
  async getPagesByRev(id: string, rev: string) {
    const rs = Acan.clone(await this.Model.findOne({id, rev}).select('pages'))
    // this.urlToS3(rs)
    return rs
  }
  async googleInit(googleId: string) {
    const {rt, token, exp}: any = (await this.app.service('user-token').Model.findOne({sub: googleId}).select(['rt', 'token', 'exp'])) ?? {}
    if (!rt) return await Promise.reject(new GoogleNoPermission('refresh token not exists in googleInit', googleId)) //return logger.warn('no refresh_token'), {message: 'refresh token not exists', googleId}
    const oauth2Client = new google.auth.OAuth2(...this.authConf)
    const conf: any = {refresh_token: rt}
    if (new Date(exp).getTime() > Date.now() + 3000000) conf.access_token = token
    oauth2Client.setCredentials(conf)
    logger.warn('exp at: ', new Date(exp).getTime() - Date.now(), 'ms', googleId)
    return oauth2Client
  }
  async googleToken(googleId: string) {
    const auth = await this.googleInit(googleId)
    if (!auth.credentials) return
    if (auth.credentials.access_token) return auth.credentials.access_token
    const rs = await auth.getAccessToken()
    await this.app.service('user-token').Model.updateOne({sub: googleId}, {$set: {token: rs.token, exp: Date.now() + 7200000}})
    logger.warn('up access_token', googleId, rs.status, rs?.token, Object.keys(auth.credentials))
    return rs?.token
  }
  async driveInit(googleId: string) {
    const auth: any = await this.googleInit(googleId)
    return google.drive({version: 'v3', auth})
  }
  async googleInit2(googleId: string) {
    // const {rt, token, exp}: any = (await this.app.service('user-token').Model.findOne({sub: googleId}).select(['rt', 'token', 'exp'])) ?? {}
    // if (!rt) return await Promise.reject(new GoogleNoPermission('refresh token not exists in googleInit', googleId)) //return logger.warn('no refresh_token'), {message: 'refresh token not exists', googleId}
    // const ubj = new URL(this.app.get('api3Url') + '/slide/')
    // ubj.searchParams.set('rt', rt)
    // ubj.searchParams.set('token', token)
    // ubj.searchParams.set('exp', exp)
    // ubj.searchParams.set('googleId', googleId)
    // return ubj
  }
  async googleReq(uri: string, googleId: string, data: any) {
    // const ubj = await this.googleInit2(googleId)
    const ubj = new URL(this.app.get('api3Url') + '/slide/')
    ubj.searchParams.set('googleId', googleId)
    ubj.pathname += uri
    for (const key in data) {
      ubj.searchParams.set(key, data[key])
    }
    return (await got(ubj.href, {json: true})).body
  }
  async getTestGoogleToken({name}: any, params: Params) {
    const rs = await this.googleReq('googleToken', params.user?.google, {})
    return rs
  }
  async getTestCreateSlide({name, hash}: any, params: Params) {
    const rs = await this.googleReq('createSlide', params.user?.google, {name, hash})
    return {id: rs.id, url: `https://docs.google.com/presentation/d/${rs?.id}/edit`}
  }
  async getCheckLast({sid}: any, params: Params) {
    const [, oldHash, sourceSid] = sid.split(':') ?? []
    const doc: any = await this.Model.findOne({id: sourceSid}).sort({_id: -1}).select('hash')
    return oldHash === doc.hash
  }
  // 初始化PPT
  async getCreateSlide({unitId, name, sid}: any, params: Params) {
    const [type, oldHash, sourceSid] = sid?.split(':') ?? []
    // 获取最新的 hash 进行初始化
    let hash
    if (sourceSid) {
      const doc: any = await this.Model.findOne({id: sourceSid}).sort({_id: -1}).select('hash')
      hash = doc.hash
    }
    if (type === 'disable') return {message: 'Is not your slides, can not create slides', type, sid} // 协同的课件不能初始化
    const ubj = new URL(this.app.get('api3Url') + '/slide/createSlide')
    ubj.searchParams.set('googleId', params.user?.google)
    if (name) ubj.searchParams.set('name', name)
    if (hash) ubj.searchParams.set('hash', hash)
    const rs = (await got(ubj.href, {json: true})).body
    if (rs.message) return rs
    // 未初始化的课件更新为新的slides.id
    if (rs?.id) await this.app.service('unit').Model.updateOne({_id: unitId}, {$set: {sid: rs?.id}})
    // 拉取slides
    this.app
      .service('unit')
      .Model.findById(unitId)
      .select(['source'])
      .then((unitDoc: any) => {
        if (unitDoc.source) return {}
        // 自己的课件在初始化后立马保存并同步materials
        return this.getSyncSlide({id: rs.id}, params)
      })
      .then(async ({pages}: any) => {
        if (!pages) return
        if (!sourceSid) return
        // 同步mateiral
        const oldSlides: any = await this.app.service('slides').Model.findOne({id: sourceSid}).select(['pages._id'])
        const oldPageMap: any = oldSlides.pages.map((v: any) => v._id)
        const arr = Acan.clone(await this.app.service('materials').Model.find({id: sourceSid}))
        for (let i = 0; i < arr.length; i++) {
          const o: any = arr[i]
          o.id = rs.id
          o.page = pages[oldPageMap.indexOf(o.page)]._id
          o.uid = params.user?._id
          for (const k of ['createdAt', 'updatedAt', '__v', '_id']) {
            delete o[k]
          }
          await this.app.service('materials').create(o, params)
        }
      })
    return {id: rs.id, url: `https://docs.google.com/presentation/d/${rs?.id}/edit`}
  }

  async getCopySlide({unitId, name, sid}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    if (!sid && unitId) {
      const rs: any = await this.app.service('unit').Model.findById(unitId, null, options).select('sid')
      if (rs?.sid) sid = rs.sid
    }
    if (!sid) return Promise.reject(new GeneralError('no sid!'))
    const ubj = new URL(this.app.get('api3Url') + '/slide/copyFile')
    ubj.searchParams.set('googleId', params.user?.google)
    if (name) ubj.searchParams.set('name', name)
    ubj.searchParams.set('fileId', sid)
    const rs = (await got(ubj.href, {json: true})).body
    return rs
  }
  // up pptx to create slides
  async rmFile(fileId: string, params: Params) {
    const drive = await this.driveInit(params.user?.google)
    const rs = await drive.files.delete({fileId, supportsAllDrives: true}).catch(({response}: any) => {
      if (response?.status && response.status !== 200) {
        const message = response.data.error_description || response.data.error.message || response.data.error
        logger.warn('file.delete error', response.data, message)
        const data: any = {message, status: response.status} // login: message.includes('Token ')
        if (response.status === 401)
          return Promise.reject(new GoogleNoPermission(`refresh token not exists in rmFile, status: ${response.status}, ${message}`, params.user?.google))
        return data
      }
      return logger.warn('file.delete', fileId, response), response
    })
    return rs
  }
  async test(googleId: string, method: string, data: any) {
    const drive = await this.driveInit(googleId)
    const rs = await drive.files[method](...data)
    return rs
  }
  /*
await App.service('tool').get('test', { query: {
  model: 'slides', method: 'createSlides', args: ["104060313456997370994", 'test333']
}})
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'list', [{
  pageSize: 10, fields: 'nextPageToken, files(id, name, mimeType)'
}]]};
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'get', [{
  fileId: "1Ya0inQGMiiG3bjeT9ZYSK5OYtcUoUsL8eJwomdHncLQ", mimeType: "application/vnd.oasis.opendocument.presentation"
}]]};
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'copy', [{
  fileId: "1Ya0inQGMiiG3bjeT9ZYSK5OYtcUoUsL8eJwomdHncLQ", resource: {name: 'test123', id: ''}
}]]};
pptx to slide
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'copy', [
  { title: copyTitle, parents: [{id: 'root'}] }, '1xtrPouTxNuPeWVa2GGxO2DHHm2KQ-YXf'
]]};
for download file
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'get', [{
  fileId: "1Ya0inQGMiiG3bjeT9ZYSK5OYtcUoUsL8eJwomdHncLQ", alt: "media"
}]]}
for download docs
var query = {model: 'slides', method: 'test', args: ["104060313456997370994", 'export', [{
  fileId: "1Ya0inQGMiiG3bjeT9ZYSK5OYtcUoUsL8eJwomdHncLQ", mimeType: "application/vnd.oasis.opendocument.presentation"
}]]}
App.service('tool').timeout=120000;
const rs = await App.service('tool').get('test', { query });logger.log(rs)
*/
  async slideInit(googleId: string) {
    const auth: any = await this.googleInit(googleId)
    return google.slides({version: 'v1', auth})
  }
  picFormat(one: any) {
    return one
    if (!one?.pages) return
    for (const page of one.pages) {
      if (!page.pic) continue
      const path = !isDev ? 'line/' : 'dev/'
      page.url = `${this.app.get('cdnUrl')}${path}${page.pic.replace(path, '')}`
      delete page.pic
      delete page.size
    }
    return one
  }
  // { _id, pages }
  // 下载google临时图片到文件存储
  async urlToS3(one: any, force: boolean = false, params?: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    if (!one?.pages) return logger.warn('no pages', one)
    const pages = Acan.clone(one.pages)
    for (const page of pages) {
      if (!page.url) {
        // logger.info('slides.urlToS3: no page.url', page._id)
        continue
      }
      let rs = (await got(this.app.get('api3Url') + `/tool/upfileUrl?url=${encodeURIComponent(page.url)}`, {json: true})).body
      if (!rs) rs = (await got(this.app.get('api3Url') + `/tool/upfileUrl?url=${encodeURIComponent(page.url)}`, {json: true})).body
      if (rs) {
        page.pic = rs._id
        page.size = rs.size
        delete page.url
      }
    }
    logger.info('download slides url ok', pages.length, one._id)
    await this.app.service('slides').Model.updateOne({_id: one._id}, {$set: {pages}}, options)
  }
  async getSlideToS3({id, uid, google}: any, params: Params) {
    if (!uid) uid = params.user?._id
    if (!google && uid) google = await this.app.service('users').getGoogle({_id: uid}, params)
    if (!google) return {message: 'not find googleId', uid}
    logger.warn('slideToS3', google, id)

    const ubj = new URL(this.app.get('api3Url') + '/slide/downToS3')
    ubj.searchParams.set('googleId', google)
    ubj.searchParams.set('id', id)
    return (await got(ubj.href, {json: true})).body
  }

  async getSyncSlide({id, taskId, google, isSaveChange}: any, params: Params) {
    const ubj = new URL(this.app.get('api3Url') + '/slide/syncSlide')
    ubj.searchParams.set('googleId', google || params.user?.google)
    if (id) ubj.searchParams.set('id', id)
    if (taskId) ubj.searchParams.set('taskId', taskId)
    const rs = (await got(ubj.href, {json: true})).body
    if (rs._id) this.urlToS3({_id: rs._id, pages: rs.pages}, true, params)
    return rs
  }
  async getTestSlide({sid}: any, params: Params) {
    const cache = this.app.service('cache')
    const doc = await cache.Model.findOne({'data.presentationId': sid})
    return doc
  }
  getTestPic({}: any) {
    return this.Model.distinct('pages.pic')
  }
}
