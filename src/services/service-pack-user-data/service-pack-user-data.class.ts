import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {GeneralError, NotFound} from '@feathersjs/errors'
import logger from '../../logger'

export class ServicePackUserData extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async cron1({}: any, params?: Params): Promise<any> {
    const rs = await this.getAutoExpireSoon()
    return rs
  }
  // 服务次数过期的，自动扣除
  async getAutoExpireSoon() {
    const arr: any = Acan.clone(await this.Model.find({expired: {$lt: new Date()}, status: 0}).limit(1000))
    for (const one of arr) {
      this.useByDoc(one, 'expired')
    }
    return arr || {}
  }
  // 通过 消耗次数
  async useByDoc(packUserData: any, type: String) {
    const {_id, packUser} = packUserData
    await this.Model.updateOne({_id}, {$set: {status: -1}})
    const doc: any = Acan.clone(await this.app.service('service-pack-user').Model.findById(packUser))
    if (!doc) return Promise.reject(new NotFound())
    // 找到最近过期的次数
    const nextDoc: any = this.Model.findOne({_id: {$ne: _id}, packUser, status: 0})
      .sort({expired: 1})
      .select('expired')
    const expireSoon = nextDoc?.expired ?? undefined
    const post: any = {$inc: {used: 1}, $set: {}}
    if (expireSoon) post.$set.expireSoon = expireSoon
    if (doc.total === doc.used + 1) {
      post.$set.status = false // 最后一次，服务包同时变为失效
    }
    await this.app.service('service-pack-user').Model.updateOne({_id: doc._id}, post)
    if (Acan.isDefined(post.$set?.status)) {
      if (doc.pid) {
        // 子服务包变化
        await this.app.service('service-pack-user').checkMaterPackUser(doc.pid)
      } else {
        // 更新服务包有效统计
        await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': -1})
      }
    }
    await this.app
      .service('service-pack-user-logs')
      .create({times: -1, type, updatedAt: new Date(), expireSoon, remaining: doc.total - doc.used, packUserData, packUser})

    // 销售跟踪列表数据处理
    this.app.service('sales-follow-up').handleFollowUpPaid(packUser)
  }
  // 扣取次数, 同时更新最近过期的卡
  async used({booking, packUser, order, times = 1, type, servicer, oldSession, start, serviceTicket, isClaim = false}: any, params: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    times = parseInt(times)
    if (times < 1) return Promise.reject(new GeneralError('used times error'))
    // 找出扣取的次数
    const query: any = {packUser, status: 0}
    if (order) query.order = order
    if (serviceTicket) query.serviceTicket = serviceTicket
    const arr: any = Acan.clone(await this.Model.find(query, null, options).sort({expired: 1}).limit(times))
    if (arr.length < times) return Promise.reject(new GeneralError('Not enough times')) // 次数不足了

    // 扣取次数
    const $in = arr.map((v: any) => v._id)
    await this.Model.updateMany({_id: {$in}}, {$set: {status: 1}}, options)
    // 绑定 booking 的消费的次数数据
    const bookingPost: any = {packUserData: $in}
    if (booking) await this.app.service('service-booking').Model.updateOne({_id: booking}, {$set: bookingPost}, options)
    // 找到下一个最近的次数
    const nextDoc: any = await this.Model.findOne({packUser, status: 0}, null, options).sort({expired: 1})
    const packPost: any = {$inc: {used: times}, $set: {}}
    if (nextDoc) packPost.$set.expireSoon = nextDoc.expired
    const doc: any = await this.app.service('service-pack-user').Model.findById(packUser, null, options).select(['pid', 'snapshot._id'])
    if (!nextDoc) packPost.$set.status = false // 最后一次，服务包同时变为失效
    // if (tasks) packPost.tasks = tasks
    await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, packPost, options)
    if (Acan.isDefined(packPost.$set.status)) {
      if (doc.pid) {
        // 子服务包变化
        await this.app.service('service-pack-user').checkMaterPackUser(doc.pid, params)
      } else {
        // 更新服务包有效统计
        await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': -1}, params)
      }
    }

    const remaining = await this.Model.countDocuments({packUser, status: 0}, options)
    // 写入日志
    const logs: any = {packUser, times: -times, type, remaining, packUserData: arr}
    if (nextDoc) logs.expireSoon = nextDoc.expired
    if (start) logs.start = start
    if (servicer) logs.servicer = await this.app.service('users').uidToInfo(servicer, params)
    if (oldSession?.name) logs.name = oldSession.name
    await this.app.service('service-pack-user-logs').create(logs, {mongoose: {session: params?.mongoose?.session}})

    // 销售跟踪列表数据处理
    this.app.service('sales-follow-up').handleFollowUpPaid(packUser, params)
    // 同步ticket数据
    if (!isClaim) {
      this.app.service('service-pack-ticket').updateCount({userData: arr}, params)
    }
  }
  // 批量增加次数数据
  async add(
    {
      packUser,
      packUserData,
      packUserTasks, // Lecture包复购的课件id数组
      isNew = false,
      order,
      payMethod,
      type = 'order',
      times = 1,
      servicer,
      oldSession,
      start,
      isPromotion = false,
      serviceTicket,
      isClaim = false,
    }: any,
    params: Params
  ) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const data = []
    const packUserDoc: any = Acan.clone(
      await this.app.service('service-pack-user').Model.findById(packUser, null, options).select(['snapshot', 'status', 'pid'])
    )

    let freq, packId
    if (packUserDoc.pid) {
      const packUserDocParent: any = await this.app
        .service('service-pack-user')
        .Model.findById(packUserDoc.pid, null, options)
        .select(['snapshot', 'status', 'pid'])
      freq = packUserDocParent.snapshot.freq
      packId = packUserDocParent.snapshot._id
    } else {
      freq = packUserDoc.snapshot.freq
      packId = packUserDoc.snapshot._id
    }
    // 获取未过期的最后一次
    const lastDoc: any = await this.Model.findOne({packUser, expired: {$gte: new Date()}}, null, options).sort({expired: -1})
    // 存在则使用最后一次作为开始时间
    const nt = !lastDoc ? Date.now() : new Date(lastDoc.expired).getTime()
    // 取消，超时的时候，取出消耗的次数数据
    if (packUserData) {
      const arr: any = Acan.clone(await this.Model.find({_id: {$in: packUserData}}, null, options))
      for (let i = 0; i < arr.length; i++) {
        data.push({order: arr[i].order, payMethod: arr[i].payMethod, packUser: arr[i].packUser, expired: new Date(nt + (i + 1) * freq * 86400000)})
      }
    } else {
      for (let i = 0; i < times; i++) {
        data.push({order, payMethod, packUser, expired: new Date(nt + (i + 1) * freq * 86400000), serviceTicket})
      }
    }
    const rs = Acan.clone(await this.create(data, params))
    logger.info(rs, data)
    // const rs = await this.Model.find({_id: {$in: rsList.map((v: any) => v._id)}})
    // 记录赠送的服务类型到用户信息上
    // freeServiceType 结构变化，记录订单 https://github.com/zran-nz/bug/issues/5025
    // if (isPromotion) {
    //   await this.app.service('users').Model.updateOne({_id: params.user?._id}, {$set: {[`freeServiceType.${packId}`]: order}})
    // }

    let expireSoon
    let remaining // 剩余次数
    if (isNew) {
      // 首次创建服务包
      expireSoon = data[0].expired
      remaining = times
      const $set: any = {expireSoon, total: times}
      if (payMethod === 'cash') $set.payMethod = payMethod
      // 更新服务包统计
      await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, {$set}, options)
    } else {
      let firstDoc: any = await this.Model.findOne({packUser, status: 0}, null, options).sort({expired: 1})
      expireSoon = firstDoc.expired
      remaining = await this.Model.countDocuments({packUser, status: 0}, options)
      const post: any = {$inc: {total: times}}
      const $set: any = {expireSoon}
      if (payMethod === 'cash') $set.payMethod = payMethod
      // Lecture包补买/复购/订单取消逻辑
      if (packUserDoc.pid && !Acan.isEmpty(packUserTasks)) {
        $set.tasks = this.app.service('service-pack-user').taskInsert({packUserDoc, packUserTasks})
        // 取消该lecture包下已经预约的课
        if (type === 'order') await this.app.service('service-booking').lectureCancelByOrder({packUserDoc}, params)
      }
      if (!packUserDoc.status) {
        $set.status = true // 过期的服务包，需要激活
      }
      // 更新服务包统计
      await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, {$set, ...post}, options)
      if (Acan.isDefined($set.status)) {
        if (packUserDoc.pid) {
          // 子服务包变化
          await this.app.service('service-pack-user').checkMaterPackUser(packUserDoc.pid, params)
        } else {
          // 更新服务包有效统计
          await this.app.service('service-pack').incCount(packId, {'count.valid': 1}, params)
        }
      }
    }
    const logs: any = {packUser, times, type, updatedAt: new Date(), expireSoon, remaining, packUserData: rs}
    if (start) logs.start = start
    if (servicer) logs.servicer = await this.app.service('users').uidToInfo(servicer)
    if (oldSession?.name) logs.name = oldSession.name
    await this.app.service('service-pack-user-logs').create(logs)

    // 销售跟踪列表数据处理
    this.app.service('sales-follow-up').handleFollowUpPaid(packUser)
    // 同步ticket数据
    if (!isClaim) {
      this.app.service('service-pack-ticket').updateCount({userData: rs})
    }
    return rs
  }
  // 代课服务批量增加次数数据
  async addSubstitute({packUser, isNew = false, order, payMethod, type = 'order', times = 1, servicer, oldSession, start}: any, params: Params) {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    let packUserDoc: any = Acan.clone(
      await this.app.service('service-pack-user').Model.findById(packUser, null, options).select(['snapshot', 'status', 'pid', 'total', 'used'])
    )
    let packId
    if (packUserDoc.pid) {
      const packUserDocParent: any = await this.app
        .service('service-pack-user')
        .Model.findById(packUserDoc.pid, null, options)
        .select(['snapshot', 'status', 'pid', 'total', 'used'])
      packId = packUserDocParent.snapshot._id
    } else {
      packId = packUserDoc.snapshot._id
    }

    let remaining // 剩余次数
    if (isNew) {
      // 首次创建服务包
      remaining = times
      const $set: any = {total: times}
      if (payMethod === 'cash') $set.payMethod = payMethod
      // 更新服务包统计
      await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, {$set}, options)
    } else {
      const post: any = {$inc: {}}
      if (type === 'order') {
        // 加购
        post.$inc.total = times
      } else {
        // 退还
        post.$inc.used = -times
      }
      const $set: any = {}
      if (!packUserDoc.status) {
        $set.status = true // 过期的服务包，需要激活
      }
      if (payMethod === 'cash') $set.payMethod = payMethod
      // 更新服务包统计
      packUserDoc = await this.app
        .service('service-pack-user')
        .Model.findOneAndUpdate({_id: packUser}, {$set, ...post}, {new: true, session: params?.mongoose?.session})
      remaining = packUserDoc.total - packUserDoc.used

      if (Acan.isDefined($set.status)) {
        if (packUserDoc.pid) {
          // 子服务包变化
          await this.app.service('service-pack-user').checkMaterPackUser(packUserDoc.pid, params)
        } else {
          // 更新服务包有效统计
          await this.app.service('service-pack').incCount(packId, {'count.valid': 1}, params)
        }
      }
    }
    const logs: any = {packUser, times, type, updatedAt: new Date(), remaining, packUserSnapshot: packUserDoc}
    if (start) logs.start = start
    if (servicer) logs.servicer = await this.app.service('users').uidToInfo(servicer, params)
    if (oldSession?.name) logs.name = oldSession.name
    await this.app.service('service-pack-user-logs').create(logs, {mongoose: {session: params?.mongoose?.session}})
  }

  // 代课服务扣取次数, 同时更新最近过期的卡
  async usedSubstitute({packUser, times = 1, type, servicer, oldSession, start, compensation}: any, params: Params) {
    const options = params?.mongoose?.session ? {session: params.mongoose.session} : {}
    times = parseInt(times)
    if (times < 1) return Promise.reject(new GeneralError('used times error'))
    const packUserService = this.app.service('service-pack-user')
    const packUserDoc: any = Acan.clone(await packUserService.Model.findById(packUser, null, options).select(['snapshot', 'status', 'pid', 'total', 'used']))
    // 扣取后的剩余次数
    const remaining = packUserDoc.total - packUserDoc.used - times
    if (remaining < 0) return Promise.reject(new GeneralError('Not enough times')) // 分钟数不足了
    // 找到下一个最近的次数
    const nextDoc: any = await this.Model.findOne({packUser, status: 0}, null, options).sort({expired: 1})
    const packPost: any = {$inc: {used: times}, $set: {}}
    let packId
    if (nextDoc) packPost.$set.expireSoon = nextDoc.expired
    if (remaining === 0) {
      packPost.$set.status = false // 次数用完了
      if (packUserDoc.pid) {
        const packUserDocParent: any = await this.app
          .service('service-pack-user')
          .Model.findById(packUserDoc.pid, null, options)
          .select(['snapshot', 'status', 'pid', 'total', 'used'])
        packId = packUserDocParent.snapshot._id
      } else {
        packId = packUserDoc.snapshot._id
      }
    }
    await this.app.service('service-pack-user').Model.updateOne({_id: packUser}, packPost, options)
    if (Acan.isDefined(packPost.$set.status)) {
      if (packUserDoc.pid) {
        // 子服务包变化
        await this.app.service('service-pack-user').checkMaterPackUser(packUserDoc.pid, params)
      } else {
        // 更新服务包有效统计
        await this.app.service('service-pack').incCount(packId, {'count.valid': -1}, params)
      }
    }

    // 写入日志
    const logs: any = {packUser, times: -times, type, remaining}
    if (nextDoc) logs.expireSoon = nextDoc.expired
    if (start) logs.start = start
    if (compensation) logs.compensation = compensation
    if (servicer) logs.servicer = await this.app.service('users').uidToInfo(servicer, params)
    if (oldSession?.name) logs.name = oldSession.name
    if (oldSession?._id) logs.session = oldSession._id
    await this.app.service('service-pack-user-logs').create(logs, {mongoose: {session: params?.mongoose?.session}})
  }
}
