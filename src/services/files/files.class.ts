import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'

export class Files extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async userFileInfo({arr, uid}: any, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const tkey = `title.${uid}`
    const rs: any = await this.Model.find({_id: {$in: arr}}, null, options).select(['ext', 'size', 'mime', 'createdAt', tkey])
    for (const one of rs) {
      one.title = one.title?.[uid] ?? ''
    }
    return rs
  }
  async getFileInfo(arr: String[], params: Params): Promise<any> {
    if (Acan.isEmpty(arr)) return []
    const user = params.user ?? {}
    const tkey = `title.${user._id}`
    const rs: any = await this.Model.find({_id: {$in: arr}}).select(['ext', 'size', 'mime', 'createdAt', tkey])
    for (const one of rs) {
      one.title = one.title?.[user._id] ?? ''
    }
    return rs
  }
}
