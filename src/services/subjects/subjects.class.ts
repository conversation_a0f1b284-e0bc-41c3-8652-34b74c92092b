import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {ObjectID} from 'bson'
import {Params} from '@feathersjs/feathers'

export class Subjects extends Service {
  app: Application
  selectList: string[]
  publishKeys: string[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.publishKeys = ['standard', 'topic', 'standardLevel', 'topicLevel']
    this.selectList = [
      'uid',
      'del',
      'name',
      'subtitle',
      'curriculum',
      'participants',
      'grade',
      'standardLevel',
      'topicLevel',
      'count',
      'code',
      'source',
      'publish',
      'subjectCode',
      'coordinator',
      'updatedAt',
      'createdAt',
    ]
  }
  // pd大纲 通过code获取数据
  async getByCode({subjectCode}: any, params: Params) {
    if (!subjectCode) return null
    const doc: any = Acan.clone(await this.Model.findOne({uid: '1', subjectCode}))
    Object.assign(doc, doc.snapshot)
    delete doc.snapshot
    return doc
  }
  async getSubId() {
    return new ObjectID().toString()
  }
  async setSnapshot(_id: String, data: any) {
    const doc: any = Acan.clone(await this.Model.findById(_id))
    const snapshot: any = {}
    if (doc.snapshot) {
      for (const key of ['_id', ...this.publishKeys]) {
        if (doc.snapshot[key]) snapshot[key] = doc.snapshot[key]
      }
    }
    const publish = doc.publish ?? []
    function setKey(key: any) {
      if (!Acan.isEmpty(doc[key])) {
        if (!publish.includes(key)) publish.push(key)
      } else {
        const i = publish.indexOf(key)
        if (i !== -1) publish.splice(i, 1)
      }
      snapshot[key] = doc[key] || []
      snapshot[`${key}Level`] = doc[`${key}Level`] || []
    }
    if (data.snapshot === true) {
      setKey('standard')
      setKey('topic')
    } else {
      setKey(data.snapshot)
    }
    data.publish = publish
    data.snapshot = snapshot
  }
  async idToParticipants($in: any, params?: Params): Promise<any> {
    const session = params?.mongoose?.session
    const options = session ? {session} : {}
    const query: any = {}
    if (Acan.isObjectId($in[0])) {
      query._id = {$in}
    } else {
      return this.app.get('pdSubjectCodeMap')[$in[0]]?.participants
    }
    const rs: any = await this.Model.findOne(query, null, options).select('participants')
    if (!rs) return
    return rs.participants
  }
  async getGradesCount({school}: any, params: Params) {
    const uid = school || params.user?._id
    if (!uid) return {}
    const arr: any = await this.Model.find({uid}).select(['grade'])
    const rs: any = {}
    for (const o of arr) {
      if (o.grade)
        for (const grade of o.grade) {
          if (!rs[grade]) rs[grade] = 1
          else rs[grade]++
        }
    }
    return rs
  }

  /**
   * 判断Topic数据是否被删,只判断每层_id字段,不判断增加
   */
  matchTopic(oldData: any, newData: any, targetLevel = 1, currentLevel = 1, labels: any = []) {
    let oldMap: any = {}
    let newMap: any = {}
    for (let i = 0; i < oldData.length; i++) {
      const oldItem = oldData[i]
      if (oldItem && oldItem._id) {
        oldMap[oldItem._id] = oldItem
      }
    }
    for (let i = 0; i < newData.length; i++) {
      const newItem = newData[i]
      newMap[newItem._id] = newItem
    }
    for (let key in oldMap) {
      if (oldMap[key] && !newMap[key]) {
        labels.push(oldMap[key].name)
      } else {
        if (targetLevel > currentLevel && oldMap[key].child && oldMap[key].child.length > 0) {
          let res: any = this.matchTopic(oldMap[key].child, newMap[key].child, targetLevel, currentLevel + 1, labels)
          labels = res.labels
        }
      }
    }
    return {
      labels,
    }
  }
  async sendAll(doc: any, tpl: string, labels: any, params: Params): Promise<any> {
    const {_id} = doc
    let query: any = {status: [1, 2]}
    if (labels.length > 0) {
      query['topic.label'] = {$in: labels}
    } else {
      query['subject'] = _id
    }
    let authData = await this.app.service('service-auth').Model.find(query)
    let url = ''
    if (tpl === 'VerificationStatusChanged') url = `${SiteUrl}/v2/account/teacher/auth/introduction`

    for (let i = 0; i < authData.length; i++) {
      const authItem: any = authData[i]
      let name = await this.app.service('service-auth').getAuthName(authItem)
      this.send({uid: authItem.uid, name: name.join('-'), url}, tpl, params)
    }
    // 删除已经申请的认证项, #4754
    // 已经认证通过的不删除，#4860
    query.status = {$ne: 2}
    this.app.service('service-auth').Model.deleteMany(query).then()
  }
  async send(doc: any, tpl: string, params: Params): Promise<any> {
    const {uid, name, url} = doc
    const user: any = await this.app.service('users').uidToInfo(uid)
    return await this.app.service('notice-tpl').mailto(tpl, user.email, {username: user.name.join(' '), name: name, url: url}, params.user?._id)
  }
  async getCoordinator({school}: any, params?: Params): Promise<any> {
    let subjectsData: any = await this.Model.find({uid: school, coordinator: {$exists: true}})
      .sort({updatedAt: -1})
      .lean()
    let list: any = []
    for (let i = 0; i < subjectsData.length; i++) {
      const item = subjectsData[i]
      for (let j = item.coordinator.length - 1; j >= 0; j--) {
        const coordinator = item.coordinator[j]
        let user: any = await this.app.service('users').uidToInfo(coordinator)
        list.push({
          ...item,
          userInfo: user,
        })
      }
    }
    return list
  }
}
