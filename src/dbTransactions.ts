import {GeneralError} from '@feathersjs/errors'
import {HookContext} from '@feathersjs/feathers'
import {ClientSession} from 'mongoose'

declare module '@feathersjs/feathers' {
  interface HookContext {
    mongooseSession?: ClientSession
  }
}

// HOOK-BASED TRANSACTION FUNCTIONS

// Before hook - Start transaction
export const startTransaction = () => {
  return async (context: HookContext) => {
    const {app, params} = context

    // Get Mongoose client from your app setup
    const mongooseClient = app.get('mongooseClient')
    console.log('startTransaction', typeof app, typeof mongooseClient)
    if (!mongooseClient) {
      throw new Error('Mongoose client not found')
    }

    // Start session and transaction
    const session = await mongooseClient.startSession()
    session.startTransaction()

    params.mongoose = params.mongoose || {}
    params.mongoose.session = session

    return context
  }
}

// After hook - Commit transaction
export const commitTransaction = () => {
  return async (context: HookContext) => {
    console.log('commitTransaction', typeof context.params?.mongoose?.session)
    const mongooseSession = context.params?.mongoose?.session

    if (mongooseSession) {
      try {
        console.log('commitTransaction 1', typeof mongooseSession)
        await mongooseSession.commitTransaction()
        console.log('commitTransaction 2')
      } catch (error) {
        console.error('Transaction commit failed:', error)
        throw new GeneralError('Transaction commit failed')
      } finally {
        await mongooseSession.endSession()
      }
    }
  }
}

// Error hook - Rollback transaction
export const rollbackTransaction = () => {
  return async (context: HookContext) => {
    console.log('rollbackTransaction', context.error, typeof context.params?.mongoose?.session)
    const mongooseSession = context.params?.mongoose?.session

    if (mongooseSession) {
      try {
        await mongooseSession.abortTransaction()
      } catch (error) {
        console.error('Transaction rollback failed:', error)
      } finally {
        await mongooseSession.endSession()
        console.log('Transaction session ended - ROLLBACK')
      }
    }

    return context
  }
}

// STANDALONE TRANSACTION FUNCTIONS

export interface TransactionResult<T> {
  success: boolean
  data?: T
  error?: Error
}

// Create a new transaction session
export const startTransactionSession = async (app: any): Promise<ClientSession> => {
  const mongooseClient = app.get('mongooseClient')
  const session = await mongooseClient.startSession()
  session.startTransaction()
  return session
}

// Commit a transaction session
export const commitTransactionSession = async (session: ClientSession): Promise<void> => {
  try {
    await session.commitTransaction()
    console.log('Transaction committed')
  } catch (error) {
    console.error('Transaction commit failed:', error)
    throw new Error('Transaction commit failed')
  } finally {
    await session.endSession()
    console.log('Transaction session ended - SUCCESS')
  }
}

// Rollback a transaction session
export const rollbackTransactionSession = async (session: ClientSession): Promise<void> => {
  try {
    await session.abortTransaction()
    console.log('Transaction rolled back')
  } catch (error) {
    console.error('Transaction rollback failed:', error)
  } finally {
    await session.endSession()
    console.log('Transaction session ended - ROLLBACK')
  }
}

// Execute function within a transaction
export const withTransaction = async <T>(app: any, operation: (session: ClientSession) => Promise<T>): Promise<TransactionResult<T>> => {
  const mongooseClient = app.get('mongooseClient')
  const session = await mongooseClient.startSession()

  try {
    session.startTransaction()

    const result = await operation(session)

    await session.commitTransaction()

    return {
      success: true,
      data: result,
    }
  } catch (error) {
    try {
      await session.abortTransaction()
    } catch (rollbackError) {
      console.error('Transaction rollback failed:', rollbackError)
    }

    return {
      success: false,
      error: error as Error,
    }
  } finally {
    await session.endSession()
  }
}

/*
Usage of "session":

- While calling feathers service methods:
await app.service('users').create({ name: 'John', email: '<EMAIL>' }, { mongoose: { session } });
await app.service('users').patch('1234567890', { email: '<EMAIL>' }, { mongoose: { session } });

- While calling direct mongoose methods (direct db calls):
await app.service('users').Model.create([{ name: 'John', email: '<EMAIL>' }], { session });
await app.service('users').Model.updateOne({ name: 'John' }, { email: '<EMAIL>' }, { session });
await app.service('users').Model.deleteOne({ name: 'John' }, { session });
await app.service('users').Model.findOneAndUpdate({ name: 'John' }, { email: '<EMAIL>' }, { session });

---------

Hook usage example:

export default {
  before: {
    create: [startTransaction()],
    update: [startTransaction()],
    patch: [startTransaction()],
    remove: [startTransaction()]
  },
  after: {
    create: [commitTransaction()],
    update: [commitTransaction()],
    patch: [commitTransaction()],
    remove: [commitTransaction()]
  },
  error: {
    create: [rollbackTransaction()],
    update: [rollbackTransaction()],
    patch: [rollbackTransaction()],
    remove: [rollbackTransaction()]
  }
};
---------
Standalone usage example:

const session = await startTransactionSession(this.app);
try {
  await app.service('users').Model.updateOne({ name: 'John' }, { email: '<EMAIL>' }, { session });
  await app.service('users').create({ name: 'John', email: '<EMAIL>' }, { mongoose: { session } });
  await commitTransactionSession(session);
} catch (error) {
  await rollbackTransactionSession(session);
}
*/
