"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const members_group_class_1 = require("./members_group.class");
const members_group_model_1 = __importDefault(require("../../models/members_group.model"));
const members_group_hooks_1 = __importDefault(require("./members_group.hooks"));
function default_1(app) {
    const options = {
        Model: (0, members_group_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/members-group', new members_group_class_1.MembersGroup(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('members-group');
    service.hooks(members_group_hooks_1.default);
}
exports.default = default_1;
