"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Manager = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class Manager extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    nameFormat(o) {
        if (!o.name)
            return;
        o.name.map((v, i) => {
            o.name[i] = v.toFirstUpperCase();
        });
        o.nickname = o.name.join(' ');
        return o;
    }
    async getJoin({ uid, status, avatar }, params) {
        let user = await this.app.service('users').uidToInfo(uid);
        let manager = await this.Model.findOne({ email: user.email });
        if (!manager) {
            throw new NotFound('Manager not found');
        }
        if (status == 2) {
            await this.app.service('users').Model.updateOne({ _id: uid }, { $addToSet: { roles: 'manager', managerRoles: manager.role } });
            this.send(manager, 'ManagingPortalURL', params);
        }
        return this.Model.updateOne({ email: user.email }, { uid, status, avatar, joinAt: new Date() });
    }
    async getInviteEmail({ id }, params) {
        let manager = await this.Model.findOne({ _id: id });
        if (!manager) {
            throw new NotFound('Manager not found');
        }
        return this.send(manager, 'ManagementInitiatingEmail', params);
    }
    async send(doc, tpl, params) {
        var _a;
        let { email, name, role } = doc;
        let url = '';
        if (tpl === 'ManagementInitiatingEmail')
            url = `${SiteUrl}/v2/setting/managerJoin`;
        else if (tpl === 'ManagingPortalURL')
            url = `${SiteUrl}/v2/setting/managerJoin`;
        return await this.app.service('notice-tpl').mailto(tpl, email, { username: name.join(' '), role: role, url }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    async isAgent(uid) {
        let manager = await this.Model.findOne({ uid, status: 2 });
        return manager && manager.role == 'agent' ? true : false;
    }
    async extUser(one, params) {
        one.userInfo = await this.app.service('users').uidToInfo(one.uid);
    }
}
exports.Manager = Manager;
