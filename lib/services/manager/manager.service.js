"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const manager_class_1 = require("./manager.class");
const manager_model_1 = __importDefault(require("../../models/manager.model"));
const manager_hooks_1 = __importDefault(require("./manager.hooks"));
function default_1(app) {
    const options = {
        Model: (0, manager_model_1.default)(app),
        paginate: app.get('paginate'),
        whitelist: ['$regex', '$options', '$search'],
    };
    // Initialize our service with any options it requires
    app.use('/manager', new manager_class_1.Manager(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('manager');
    service.hooks(manager_hooks_1.default);
}
exports.default = default_1;
