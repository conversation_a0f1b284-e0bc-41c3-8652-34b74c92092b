"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const search = require('feathers-mongodb-fuzzy-search');
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
function nameFormat(d) {
    if (Array.isArray(d.data)) {
        for (const one of d.data) {
            d.service.nameFormat(one);
        }
    }
    else {
        d.service.nameFormat(d.data);
    }
}
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            search({
                fields: ['name'],
            }),
        ],
        get: [hook_1.default.toClass],
        create: [nameFormat],
        update: [],
        patch: [
            async (d) => {
                let manager = await d.app.service('manager').Model.findOne({ _id: d.id }).lean();
                const { role } = d.data;
                if (role && role != manager.role) {
                    if (manager.uid) {
                        await d.app.service('users').Model.updateOne({ _id: manager.uid }, { $pull: { roles: 'manager', managerRoles: manager.role } });
                    }
                    // manager.role = role
                    // d.service.send(manager, 'ManagementInitiatingEmail', d.params)
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        if (d.result.data[i].uid)
                            await d.service.extUser(d.result.data[i]);
                    }
                }
            },
        ],
        get: [
            async (d) => {
                if (d.result.uid) {
                    await d.service.extUser(d.result);
                }
            },
        ],
        create: [
            async (d) => {
                d.service.send(d.result, 'ManagementInitiatingEmail', d.params);
            },
        ],
        update: [],
        patch: [],
        remove: [
            async (d) => {
                const { uid, role, status } = d.result;
                let user;
                try {
                    user = await d.app.service('users').uidToInfo(uid);
                }
                catch (error) { }
                if (user) {
                    await d.app.service('users').Model.updateOne({ _id: uid }, { $pull: { roles: 'manager', managerRoles: role } });
                }
                if (status == 2) {
                    d.service.send(d.result, 'RemovingFromManagingPortal', d.params);
                }
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
