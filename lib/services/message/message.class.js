"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class Message extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extUser(one, params) {
        one.userInfo = await this.app.service('users').Model.findOne({ _id: one.uid });
    }
    async send(doc, params) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        const { type, rid, role, message } = doc;
        let sender = await this.app.service('users').Model.findOne({ _id: (_a = params.user) === null || _a === void 0 ? void 0 : _a._id });
        // service-auth-premium
        if (type == 'service-auth-premium') {
            let business = await this.app.service('service-auth').Model.findOne({ _id: rid });
            let name = await this.app.service('service-auth').getAuthName(business);
            if (role == 'admin') {
                let user = await this.app.service('users').Model.findOne({ _id: business.uid });
                let url = `${SiteUrl}/v2/account/teacher/auth/edit/verification`;
                return await this.app.service('notice-tpl').mailto('MessageToUserRegardingPremiumContentVerification', user, {
                    username: user.name.join(' '),
                    name: name.slice(0, name.length - 1).join('-'),
                    service_type: name[name.length - 1],
                    url,
                }, (_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
            }
            if (role == 'user') {
                let user;
                if (business.follower) {
                    user = await this.app.service('users').Model.findOne({ _id: business.follower });
                }
                else {
                    user = await this.app.service('users').Model.findOne({ email: '<EMAIL>' });
                }
                let url = `${SiteUrl}/v2/sys/premium-content?tab=1&currentVerification=academic`;
                return await this.app.service('notice-tpl').mailto('MessageToClasscipeRegardingPremiumContentVerification', user, {
                    username: user.name.join(' '),
                    name: name.slice(0, name.length - 1).join('-'),
                    service_type: name[name.length - 1],
                    url,
                }, (_c = params.user) === null || _c === void 0 ? void 0 : _c._id);
            }
        }
        // service-pack-apply
        if (type == 'service-pack-apply') {
            let business = await this.app.service('service-pack-apply').Model.findOne({ _id: rid });
            let student = await this.app.service('users').Model.findOne({ _id: business.uid });
            let servicePack = await this.app.service('service-pack').Model.findOne({ _id: business.servicePack });
            if (role == 'admin') {
                if (business.sharedSchool) {
                    let admins = await this.app.service('school-user').Model.find({ school: business.sharedSchool, role: 'admin' });
                    for (let i = 0; i < admins.length; i++) {
                        const admin = admins[i];
                        let url = `${SiteUrl}/v2/premcpack/applicationTrackDetail/${business.servicePack}`;
                        return await this.app.service('notice-tpl').mailto('MessageToUserRegardingPremiumLectureApplicationA', admin.email, {
                            username: admin.name.join(' '),
                            studentname: student.name.join(' '),
                            name: servicePack.name,
                            url,
                        }, (_d = params.user) === null || _d === void 0 ? void 0 : _d._id);
                    }
                }
                else {
                    let url = `${SiteUrl}/v2/premcpack/myApplication`;
                    return await this.app.service('notice-tpl').mailto('MessageToUserRegardingPremiumLectureApplicationB', student, {
                        username: student.name.join(' '),
                        name: servicePack.name,
                        message,
                        url,
                    }, (_e = params.user) === null || _e === void 0 ? void 0 : _e._id);
                }
            }
            if (role == 'user') {
                let user;
                if (business.follower) {
                    user = await this.app.service('users').Model.findOne({ _id: business.follower });
                }
                else {
                    user = await this.app.service('users').Model.findOne({ email: '<EMAIL>' });
                }
                let url = `${SiteUrl}/v2/premcpack/enroll/${business.servicePack}`;
                return await this.app.service('notice-tpl').mailto('MessageToClasscipeRegardingPremiumLectureApplication', user, {
                    username: user.name.join(' '),
                    name: servicePack.name,
                    url,
                }, (_f = params.user) === null || _f === void 0 ? void 0 : _f._id);
            }
        }
        // school-plan
        if (type == 'school-plan') {
            let business = await this.app.service('school-plan').Model.findOne({ _id: rid });
            if (role == 'user') {
                let user = await this.app.service('users').Model.findOne({ email: '<EMAIL>' });
                let url = `${SiteUrl}/v2/sys/school/${rid}`;
                return await this.app.service('notice-tpl').mailto('MessageToClasscipeRegardingContentProviderSetting', user, {
                    username: user.name.join(' '),
                    schoolname: business.name,
                    url,
                }, (_g = params.user) === null || _g === void 0 ? void 0 : _g._id);
            }
            if (role == 'admin') {
                let admins = await this.app.service('school-user').Model.find({ school: rid, role: 'admin' });
                let url = `${SiteUrl}v2/account/info`;
                if (business.contentProviderStatus != 2) {
                    url = '';
                }
                for (let i = 0; i < admins.length; i++) {
                    const admin = admins[i];
                    return await this.app.service('notice-tpl').mailto('MessageToUserRegardingContentProviderSetting', admin.email, {
                        username: admin.name.join(' '),
                        url,
                    }, (_h = params.user) === null || _h === void 0 ? void 0 : _h._id);
                }
            }
        }
        // service-auth
        if (type == 'service-auth') {
            let business = await this.app.service('service-auth').Model.findOne({ _id: rid });
            let name = await this.app.service('service-auth').getAuthName(business);
            if (role == 'user') {
                let user = await this.app.service('users').Model.findOne({ email: '<EMAIL>' });
                let url = `${SiteUrl}/v2/sys/teacher-verification/${rid}`;
                return await this.app.service('notice-tpl').mailto('MessageToClasscipeRegardingServiceVerification', user, {
                    username: user.name.join(' '),
                    name: name.slice(0, name.length - 1).join('-'),
                    service_type: name[name.length - 1],
                    url,
                }, (_j = params.user) === null || _j === void 0 ? void 0 : _j._id);
            }
            if (role == 'admin') {
                let user = await this.app.service('users').Model.findOne({ _id: business.uid });
                let url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${rid}`;
                return await this.app.service('notice-tpl').mailto('MessageToUserRegardingServiceVerification', user, {
                    username: user.name.join(' '),
                    name: name.slice(0, name.length - 1).join('-'),
                    service_type: name[name.length - 1],
                    url,
                }, (_k = params.user) === null || _k === void 0 ? void 0 : _k._id);
            }
        }
        // classes 用announcement
        // if (type == 'classes') {
        //   if (role == 'admin') {
        //     let business: any = await this.app.service('classes').Model.findOne({_id: rid})
        //     let students = await this.app.service('students').Model.find({class: rid})
        //     let schoolPlan: any = await this.app.service('school-plan').Model.findOne({_id: business.school})
        //     for (let i = 0; i < students.length; i++) {
        //       const student: any = students[i]
        //       let url = ``
        //       return await this.app.service('notice-tpl').mailto(
        //         'MessageToAllRegardingClassAnnouncement',
        //         student.email,
        //         {
        //           username: student.name.join(' '),
        //           teachername: sender.name.join(' '),
        //           classname: business.name,
        //           schoolname: schoolPlan.name,
        //           url,
        //         },
        //         params.user?._id
        //       )
        //     }
        //   }
        // }
        // cloud-room
        // session-takeaway
        if (type == 'session-takeaway') {
            let session = await this.app.service('session').Model.findOne({ _id: rid });
            let teacher = await this.app.service('users').Model.findOne({ _id: session.uid });
            if (role == 'admin') {
                let url = ``;
                return await this.app.service('notice-tpl').mailto('MessageToTakeawayEvaluator', teacher, {
                    username: teacher.name.join(' '),
                    name: session.name,
                    url,
                }, (_l = params.user) === null || _l === void 0 ? void 0 : _l._id);
            }
        }
        // prompts unit self-study
        if (type == 'prompts' || type == 'unit' || type == 'self-study') {
            let business = {};
            if (type == 'prompts') {
                business = await this.app.service('prompts').Model.findOne({ _id: rid });
            }
            else if (type == 'unit') {
                business = await this.app.service('unit').Model.findOne({ _id: rid });
            }
            // else if (type == 'self-study') {
            //   business = await this.app.service('session').Model.findOne({_id: rid})
            // }
            let author = await this.app.service('users').Model.findOne({ _id: business.uid });
            if (role == 'admin') {
                let url = ``;
                return await this.app.service('notice-tpl').mailto('MessageToAuthorRegardingThePublishedContent', author, {
                    username: author.name.join(' '),
                    name: (business === null || business === void 0 ? void 0 : business.name) ? `${business === null || business === void 0 ? void 0 : business.name} of Teaching resource` : 'of Prompts',
                    url,
                }, (_m = params.user) === null || _m === void 0 ? void 0 : _m._id);
            }
            if (role == 'user') {
                let user = await this.app.service('users').Model.findOne({ email: '<EMAIL>' });
                let url = ``;
                return await this.app.service('notice-tpl').mailto('MessageToClasscipeRegardingThePublishedContent', user, {
                    username: user.name.join(' '),
                    author: author.name.join(' '),
                    name: (business === null || business === void 0 ? void 0 : business.name) ? `${business === null || business === void 0 ? void 0 : business.name} of Teaching resource` : 'of Prompts',
                    url,
                }, (_o = params.user) === null || _o === void 0 ? void 0 : _o._id);
            }
        }
    }
}
exports.Message = Message;
