"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tpl = `<div>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width:100%;background-color: rgb(246, 246, 246); color: rgb(34, 34, 34); font-family: Arial, Helvetica, sans-serif; font-size: small;">
<tbody>
<tr>
<td style="font-family: sans-serif; margin: 0px; font-size: 14px; vertical-align: top;">&nbsp;</td>
<td style="font-family: sans-serif; margin: 0px auto !important; font-size: 14px; vertical-align: top; display: block;padding: 10px;">
<div style="box-sizing: border-box; margin: 0px auto; max-width: 1100px; padding: 10px;">
<table role="presentation" style="width: 1000px; border-radius: 3px;">
<tbody>
<tr>
<td style="margin: 0px; font-size: 14px; vertical-align: top; box-sizing: border-box;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width: 100%">
<tbody>
<tr>
<td style="margin: 0px; font-size: 14px; vertical-align: top;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0"
style="width: 100%;">
<tbody>
<tr>
<td align="center" style="margin: 0px; font-size: 14px; vertical-align: top; padding: 10px 0px 5px; background-color: rgb(255, 255, 255);">
<img src="https://ci5.googleusercontent.com/proxy/uwuTf0cIDlC8c2eHg0TOJD_fjk93NG1RgxvzACGKzrT-WFGWiYeNhkP6fnzk14vk5RicL63jbg0_kEyKNuZCNMFG915pvg=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/logo.png" data-bit="iit" style="border: none; max-width: 100%; height: 50px;">
</td>
</tr>
</tbody>
</table>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
<tbody>
<tr>
<td style="margin: 0px; font-size: 14px; vertical-align: top; background: rgb(232, 252, 251); padding: 50px 40px 20px;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width: 1000px;">
<tbody>
<tr>
<td align="left" style="margin: 0px; font-size: 14px; vertical-align: top; padding: 0px 0px 0px 2em; color: rgb(19, 20, 23);">{{content}}</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width: 1000px;"><tbody><tr>
<td align="left" style="margin: 0px; font-size: 14px; vertical-align: top; background: rgb(232, 252, 251);">
<img src="https://ci6.googleusercontent.com/proxy/F2gAvd7aaU3QpdlBWKAWOnz_sN87D1uPv6hGNX29q8qo8CEhDH8gIaG2A6xZCi2TPn7ygo_t8YA-cCILhW7a2qzj0fAchjbt=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/banner.png" style="cursor: pointer; outline: 0px; border: none; max-width: 100%;">
</td></tr></tbody></table>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<div style="clear: both; margin-top: 20px; text-align: center; width: 1000px;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" style="width: 1000px;">
<tbody>
<tr>
<td style="margin: 0px; font-size: 12px; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: rgb(153, 153, 153); text-align: center;">
<a href="https://www.facebook.com/Classcipe" aria-label="facebook" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.facebook.com/Classcipe&amp;source=gmail&amp;ust=1659494800773000&amp;usg=AOvVaw0c88s-p9G5x90WZI_ejGq4" style="color: rgb(153, 153, 153); border: none; display: inline-block;">
<img src="https://ci4.googleusercontent.com/proxy/3EwIN_2m4yVtrjrBTUoeKlUc9ED8rQAIWsgn7Yqa5bug7CL4skOzvjtyPZgAusly9-uaYbc4t3QKeOO9eB0DQ7cUeCQ=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/fb.png" data-bit="iit" style="max-width: 100%; width: 30px;">
</a>&nbsp;
<a href="https://www.linkedin.com/company/classcipe/?viewAsMember=true" aria-label="linkedin" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.linkedin.com/company/classcipe/?viewAsMember%3Dtrue&amp;source=gmail&amp;ust=1659494800773000&amp;usg=AOvVaw1JvI-xPU3t8t7jsfOIXz-N" style="color: rgb(153, 153, 153); border: none; display: inline-block;">
<img src="https://ci5.googleusercontent.com/proxy/ic0-kmyYe85dABtg4aN7sk7UV5LKoYGB23NoakXHYF2viuYdz_ZSraKyI0zZDXPnDj-8cJV68OEbgJsK1MgUaXVz5bE=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/in.png" data-bit="iit" style="max-width: 100%; width: 30px;">
</a>&nbsp;
<a href="https://www.instagram.com/classcipe/" aria-label="instagram" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.instagram.com/classcipe/&amp;source=gmail&amp;ust=1659494800773000&amp;usg=AOvVaw2bgVSy8Ukx196mVjp9OhNO" style="color: rgb(153, 153, 153); border: none; display: inline-block;">
<img src="https://ci5.googleusercontent.com/proxy/DfnOBclhfX4XO0Z0OichvjaluWTi6bCppbkdAt7gqdBgYPsXrH39QhBS1bjdlOswap6-oa5i1Dl6ryyappIn38RzYSw8=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/ins.png" data-bit="iit" style="max-width: 100%; width: 30px;">
</a>&nbsp;
<a href="https://twitter.com/classcipeltd?lang=en" aria-label="twitter" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://twitter.com/classcipeltd?lang%3Den&amp;source=gmail&amp;ust=1659494800773000&amp;usg=AOvVaw0kzp-uq2-cOFLyLLEjN3L-" style="color: rgb(153, 153, 153); border: none; display: inline-block;">
<img src="https://ci4.googleusercontent.com/proxy/6hkwhFeMd22uBKLikJBPHC3yXqEpE9ZfGXavHyXOatCcTc2jiFhQAZ6G46zncuRy-SDv-IWWkewK2zbDguWGcHRgH3uDZYzrBA=s0-d-e1-ft#https://dcdkqlzgpl5ba.cloudfront.net/email/twitter.png" data-bit="iit" style="max-width: 100%; width: 30px;">
</a>
</td>
</tr>
<tr>
<td style="margin: 0px; font-size: 12px; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: rgb(153, 153, 153); text-align: center;">
<span>@2022 Classcipe All Rights Reserved. 2 Clover drive, Henderson, Auckland 0610, NZ</span>
</td>
</tr>
<tr>
<td style="margin: 0px; font-size: 12px; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: rgb(153, 153, 153); text-align: center;">
Powered by&nbsp;<a href="https://classcipe.com" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://classcipe.com" style="color: rgb(153, 153, 153); text-decoration-line: none;">Classcipe</a>.
</td>
</tr>
<tr><td style="margin: 0px; font-size: 12px; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: rgb(153, 153, 153); text-align: center">Update your&nbsp;<a href="{{host}}/v2/account/account-setting/notification"target="_blank"data-saferedirecturl="https://www.google.com/url?q=https://classcipe.com"style="text-decoration-line: none">email preferences</a>&nbsp;to choose which emails you get or&nbsp;<a href="{{host}}/v2/setting/unsubscribe/{{uid}}?type=confirm"target="_blank"data-saferedirecturl="https://www.google.com/url?q=https://classcipe.com"style="text-decoration-line: none">unsubscribe</a>&nbsp;from this type of email.</td></tr>
</tbody>
</table>
</div>
</div>
</td>
</tr>
</tbody>
</table>
</div>`;
function main(str, uid) {
    let host = SiteUrl.toString();
    return tpl
        .replace('{{content}}', str)
        .replace(/{{host}}/g, host)
        .replace(/{{uid}}/g, uid);
}
exports.default = main;
