"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoticeTpl = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const nodemailer = require('nodemailer');
const logger_1 = __importDefault(require("../../logger"));
const tpl_1 = __importDefault(require("./tpl"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const emailConf = {
    from: '<EMAIL>',
    host: 'smtp.gmail.com',
    port: '465',
    user: '<EMAIL>',
    pass: 'bnncksddfewoxpoy',
};
const emailConfSystem = {
    from: '<EMAIL>',
    host: 'smtp.gmail.com',
    port: '465',
    user: '<EMAIL>',
    pass: 'dygoxmuxclsgdier',
};
const plivo = require('plivo');
const plivo_auth_id = 'MAZTLMNJM4ZDY2NME2MW';
const plivo_auth_token = 'MmZmNGMyMTg3YzBiODM5MjViNDQ4ZWVhYzk1MjI1';
const plivoClient = new plivo.Client(plivo_auth_id, plivo_auth_token);
const plivoFrom = '16622464002';
class NoticeTpl extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getMail() {
        var _a;
        const redisClient = this.app.get('redis');
        return JSON.parse((_a = (await redisClient.SRANDMEMBER('smtp'))) !== null && _a !== void 0 ? _a : null);
    }
    async sendMail(to, title, body, isSystem = false, attachments) {
        // const email = await this.getMail()
        const email = isSystem ? emailConfSystem : emailConf;
        if (!email)
            return { message: 'No mail config' };
        let opt = {
            host: email.host,
            port: parseInt(email.port || '587'),
            secure: false, // true for 465, false for other ports
        };
        if (email.user) {
            opt.secure = true;
            opt.auth = { user: email.user, pass: email.pass };
        }
        let transporter = nodemailer.createTransport(opt);
        let user = await this.app.service('users').Model.findOne({ email: to });
        let mailOptions = {
            from: '"Classcipe" <' + email.from + '>',
            to,
            subject: title || 'Hello ✔ test mail title',
            // text: 'Hello world?', // plain text body
            html: typeof body === 'string' ? (0, tpl_1.default)(body || '<b>Hello world? This is test mail content</b>', (user === null || user === void 0 ? void 0 : user._id) || '') : body,
            list: {
                // List-Unsubscribe: <http://example.com> (Comment)
                unsubscribe: {
                    url: `${SiteUrl}/v2/setting/unsubscribe/${(user === null || user === void 0 ? void 0 : user._id) || ''}?type=confirm`,
                    comment: 'unsubscribe url',
                },
            },
        };
        if (attachments)
            mailOptions.attachments = attachments;
        const rs = await new Promise((res, rej) => {
            transporter.sendMail(mailOptions, (error, info) => {
                if (error) {
                    logger_1.default.info('sendFaild', error);
                    return res(error);
                }
                res(info);
                logger_1.default.log('Message sent: %s', info.messageId, info.response, info.accepted, info.rejected);
            });
        });
        return { email, rs };
    }
    async inbox(code, _id, data) {
        // todo send to inbox
    }
    async send(code, { _id, email }, data) {
        if (email)
            this.mailto(code, email, data);
        // if (_id) this.inbox(code, _id, data)
    }
    async resend({ code, email, mobile }, params) {
        var _a;
        const doc = await this.Model.findOne({ code });
        let isSystem = false;
        if ((doc === null || doc === void 0 ? void 0 : doc.category) === 'System notification') {
            isSystem = true;
        }
        if (email) {
            const old = await this.app.service('notice').Model.findOne({ code, 'data.mail': email }).sort({ _id: -1 });
            if (!old)
                return Promise.reject(new Error(`Not Found, resend: ${code}, ${email}`));
            const { mail, title, body } = old.data;
            const { email: emailConf, rs, message } = await this.sendMail(mail, title, body, isSystem);
            if (message)
                return { message };
            const post = { type: 'mail', code, data: { mail, title, body } };
            post.status = ((_a = rs === null || rs === void 0 ? void 0 : rs.response) === null || _a === void 0 ? void 0 : _a.substring(0, 4)) === '250 ';
            post.conf = [emailConf.from, emailConf.user, emailConf.host];
            post.rs = rs;
            await this.app.service('notice').create(post);
        }
        if (mobile) {
            const old = await this.app.service('notice').Model.findOne({ code, 'data.to': mobile }).sort({ _id: -1 });
            const { to, body } = old.data;
            await this.sendSms({ mobile: to, text: body, code }, params);
        }
        return code;
    }
    async mailto(code, userOrMail, data, from = '', phone = '') {
        var _a, _b, _c, _d, _e;
        logger_1.default.warn('mailto', code, userOrMail, data);
        let mail = Acan.isStr(userOrMail) && userOrMail.includes('@') ? userOrMail : '';
        let uid = '';
        let mobile = phone || '';
        if (userOrMail === null || userOrMail === void 0 ? void 0 : userOrMail.email) {
            mail = userOrMail.email;
            uid = userOrMail._id;
            mobile = mobile || userOrMail.mobile || userOrMail.emergencyContact;
        }
        if (!uid && mail) {
            let user = await this.app.service('users').Model.findOne({ email: mail });
            uid = user === null || user === void 0 ? void 0 : user._id;
            mobile = mobile || (user === null || user === void 0 ? void 0 : user.mobile);
        }
        const doc = await this.Model.findOne({ code });
        if (!doc)
            return { message: 'notice-tpl no exists' };
        const rpReg = /\{\{([\w\d]+)\}\}/gi;
        function rpFn(str, key, num) {
            return data[key] || '';
        }
        let result = {};
        const title = ((_a = doc.title) === null || _a === void 0 ? void 0 : _a.replace(rpReg, rpFn)) || '';
        const body = ((_b = doc.text) === null || _b === void 0 ? void 0 : _b.replace(rpReg, rpFn)) || '';
        const note = ((_c = doc.note) === null || _c === void 0 ? void 0 : _c.replace(rpReg, rpFn)) || '';
        const userEnable = await this.app.service('user-notice-setting').checkStatus(uid, code);
        // 站内信
        if (doc.enableInbox && userEnable.enableInbox) {
            let urlInbox = data.url;
            if (urlInbox) {
                if (urlInbox.indexOf('https://dev.classcipe.com') == 0) {
                    urlInbox = urlInbox.replace('https://dev.classcipe.com', '');
                }
                if (urlInbox.indexOf('https://test.classcipe.com') == 0) {
                    urlInbox = urlInbox.replace('https://test.classcipe.com', '');
                }
                if (urlInbox.indexOf('https://classcipe.com') == 0) {
                    urlInbox = urlInbox.replace('https://classcipe.com', '');
                }
            }
            const post = { type: 'inbox', code, data: { url: urlInbox, title, note } };
            post.from = from || '';
            if (uid) {
                post.to = uid;
                if (post.to)
                    result = await this.app.service('notice').create(post);
            }
        }
        // 邮件
        if (doc.enable && mail && userEnable.enableEmail) {
            let isSystem = false;
            if ((doc === null || doc === void 0 ? void 0 : doc.category) === 'System notification') {
                isSystem = true;
            }
            const { email, rs, message } = await this.sendMail(mail, title, body, isSystem);
            if (message)
                return { message };
            const post = { type: 'mail', code, data: { mail, title, body } };
            post.status = ((_d = rs === null || rs === void 0 ? void 0 : rs.response) === null || _d === void 0 ? void 0 : _d.substring(0, 4)) === '250 ';
            post.conf = [email.from, email.user, email.host];
            post.rs = rs;
            rs.ok = post.status ? 1 : 0;
            result = rs;
            await this.app.service('notice').create(post);
        }
        // 短信
        if (doc.enableSms && mobile && userEnable.enableSms) {
            if (data.url) {
                // 国内手机 替换国内域名
                let urlSms = data.url;
                if (mobile.indexOf('+86') == 0 || mobile.indexOf('86') == 0) {
                    if (urlSms.indexOf('https://dev.classcipe.com') == 0) {
                        urlSms = urlSms.replace('https://dev.classcipe.com', 'https://dev.classcipe.cn');
                    }
                    if (urlSms.indexOf('https://test.classcipe.com') == 0) {
                        urlSms = urlSms.replace('https://test.classcipe.com', 'https://test.classcipe.cn');
                    }
                    if (urlSms.indexOf('https://classcipe.com') == 0) {
                        urlSms = urlSms.replace('https://classcipe.com', 'https://classcipe.cn');
                    }
                }
                let shortLink = await this.app.service('short-link').getShortLink({ url: urlSms }, { user: { _id: uid } });
                data.url = shortLink.shortUrl;
            }
            const sms = ((_e = doc.sms) === null || _e === void 0 ? void 0 : _e.replace(rpReg, rpFn)) || '';
            await this.sendSms({ mobile, text: sms, code }, { user: { _id: uid } });
        }
        return result;
    }
    async sendSms({ uid, mobile, text, code }, params) {
        if (uid) {
            let user = await this.app.service('users').Model.findById(uid);
            if (!(user === null || user === void 0 ? void 0 : user.mobile))
                return Promise.reject(new NotFound('User mobile not found'));
            mobile = user.mobile;
        }
        let res;
        try {
            res = await plivoClient.messages.create({
                src: plivoFrom,
                dst: mobile,
                text: text,
            });
        }
        catch (error) {
            let body = {
                error,
                uid,
                mobile,
                code,
                text,
            };
            this.app.service('log').create({ type: 'node.notice.sms', ip: global.LocalIp, body, msg: 'plivo send error' }).then();
        }
        if (res) {
            await this.app.service('notice').Model.create({
                type: 'sms',
                code,
                from: plivoFrom,
                to: mobile || uid,
                data: { body: text, plivoFrom, to: mobile, apiId: res.apiId, messageUuid: res.messageUuid },
                rs: res,
                smsProvider: 'plivo',
            });
        }
        return res;
    }
    async getSendSms({ uid, mobile, text }, params) {
        return await this.sendSms({ uid, mobile, text }, params);
    }
}
exports.NoticeTpl = NoticeTpl;
