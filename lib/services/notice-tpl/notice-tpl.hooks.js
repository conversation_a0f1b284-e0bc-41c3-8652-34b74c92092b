"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const logger_1 = __importDefault(require("../../logger"));
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
const search = require('feathers-mongodb-fuzzy-search');
const mod = {
    async testMail(d) {
        var _a, _b;
        const { code } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const { _id, email } = (_b = d.params.user) !== null && _b !== void 0 ? _b : {};
        d.result = await d.service.mailto(code, email, { name: 'Test pd content', author: 'Test 111', date: new Date() }, _id);
    },
    async mailto(d) {
        var _a, _b;
        const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const { code, email } = query;
        logger_1.default.warn(code, email, query);
        d.result = await d.app.service('notice-tpl').mailto(code, email, query, (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id);
    },
};
exports.default = {
    before: {
        all: [],
        find: [
            search({
                fields: ['code', 'name', 'title'],
            }),
        ],
        get: [
            hook_1.default.toClass,
            authenticate('jwt'),
            (d) => {
                if (mod[d.id + ''])
                    return mod[d.id + ''](d);
            },
        ],
        create: [authenticate('jwt')],
        update: [authenticate('jwt')],
        patch: [authenticate('jwt')],
        remove: [authenticate('jwt')],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
