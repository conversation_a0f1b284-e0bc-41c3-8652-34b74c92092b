"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const notice_tpl_class_1 = require("./notice-tpl.class");
const notice_tpl_model_1 = __importDefault(require("../../models/notice-tpl.model"));
const notice_tpl_hooks_1 = __importDefault(require("./notice-tpl.hooks"));
function default_1(app) {
    const options = {
        Model: (0, notice_tpl_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search'],
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/notice-tpl', new notice_tpl_class_1.NoticeTpl(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('notice-tpl');
    service.hooks(notice_tpl_hooks_1.default);
}
exports.default = default_1;
